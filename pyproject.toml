[project]
name = "petsyn-server"
version = "0.1.0"
description = "PetSyn Server - A comprehensive server application for PetSyn"
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
authors = [
    {name = "PetSyn Team"}
]
keywords = ["server", "petsyn", "application"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "aiohttp>=3.12.15",
    "cnlunar>=0.2.0",
    "cryptography>=45.0.7",
    "ffmpeg>=1.4",
    "httpx>=0.28.1",
    "pyjwt>=2.8.0",
    "loguru>=0.7.3",
    "openai>=1.107.2",
    "opuslib-next>=1.1.5",
    "pyyaml>=6.0.2",
    "websockets>=15.0.1",
    "jinja2>=3.1.6",
]

[project.scripts]
petsyn-server = "petsyn_server:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]
include = ["petsyn_server*"]

[dependency-groups]
dev = [
    "ruff>=0.12.10",
]
