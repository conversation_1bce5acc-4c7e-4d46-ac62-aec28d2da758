import importlib
import pkgutil
from typing import List
from petsyn_server.config.logger import setup_logging

TAG = __name__

logger = setup_logging()


def auto_import_modules(package_name: str) -> List[str]:
    """
    Automatically import all modules within a specified package.

    Args:
        package_name (str): The name of the package, e.g., 'functions'.

    Returns:
        List[str]: A list of successfully imported module names.
    """
    # Get package path
    package = importlib.import_module(package_name)
    package_path = package.__path__

    imported_modules = []
    
    # Iterate through all modules in the package
    for _, module_name, _ in pkgutil.iter_modules(package_path):
        # Import module
        full_module_name = f"{package_name}.{module_name}"
        try:
            importlib.import_module(full_module_name)
            imported_modules.append(full_module_name)
            logger.bind(tag=TAG).debug(f"Module '{full_module_name}' loaded successfully")
        except ImportError as e:
            logger.bind(tag=TAG).error(f"Failed to import module '{full_module_name}': {e}")
    
    return imported_modules
