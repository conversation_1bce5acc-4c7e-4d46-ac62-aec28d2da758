import os
import time
import base64
from typing import Optional, Dict, Any, Union
import logging

import httpx

TAG = __name__
logger = logging.getLogger(TAG)


class DeviceNotFoundException(Exception):
    """Exception raised when a device is not found."""
    pass


class DeviceBindException(Exception):
    """Exception raised when device binding fails."""
    def __init__(self, bind_code: str):
        self.bind_code = bind_code
        super().__init__(f"Device binding exception, bind code: {bind_code}")


class ManageApiClient:
    """Singleton client for managing API connections."""
    _instance: Optional['ManageApiClient'] = None
    _client: Optional[httpx.Client] = None
    _secret: Optional[str] = None
    config: Dict[str, Any] = {}
    max_retries: int = 6
    retry_delay: float = 10.0

    def __new__(cls, config: Dict[str, Any]) -> 'ManageApiClient':
        """Singleton pattern to ensure global unique instance and support passing configuration parameters.
        
        Args:
            config: Configuration dictionary for the API client
            
        Returns:
            The singleton instance of ManageApiClient
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._init_client(config)
        return cls._instance

    @classmethod
    def _init_client(cls, config: Dict[str, Any]) -> None:
        """Initialize persistent connection pool.
        
        Args:
            config: Configuration dictionary containing API settings
            
        Raises:
            Exception: If configuration is invalid or incomplete
        """
        cls.config = config.get("manager-api", {})

        if not cls.config:
            raise Exception("manager-api configuration error")

        if not cls.config.get("url") or not cls.config.get("secret"):
            raise Exception("manager-api url or secret configuration error")

        if "your" in cls.config.get("secret", ""):
            raise Exception("Please configure manager-api secret first")

        cls._secret = cls.config.get("secret")
        cls.max_retries = cls.config.get("max_retries", 6)  # Maximum retry times
        cls.retry_delay = cls.config.get("retry_delay", 10)  # Initial retry delay (seconds)
        
        # Initialize HTTP client with proper configuration
        cls._client = httpx.Client(
            base_url=cls.config["url"],
            headers={
                "User-Agent": f"PythonClient/2.0 (PID:{os.getpid()})",
                "Accept": "application/json",
                "Authorization": f"Bearer {cls._secret}",
            },
            timeout=cls.config.get("timeout", 30),  # Default timeout 30 seconds
        )


    @classmethod
    def _request(cls, method: str, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Send a single HTTP request and handle response.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            **kwargs: Additional arguments for the request
            
        Returns:
            Dictionary containing response data or None if no data
            
        Raises:
            DeviceNotFoundException: If the device is not found
            DeviceBindException: If device binding fails
            Exception: For other API errors
        """
        if cls._client is None:
            raise RuntimeError("HTTP client is not initialized")
            
        endpoint = endpoint.lstrip("/")
        response = cls._client.request(method, endpoint, **kwargs)
        response.raise_for_status()

        result = response.json()

        # Handle business errors returned by API
        if result.get("code") == 10041:
            raise DeviceNotFoundException(result.get("msg", "Device not found"))
        elif result.get("code") == 10042:
            raise DeviceBindException(result.get("msg", "Device binding failed"))
        elif result.get("code") != 0:
            raise Exception(f"API returned error: {result.get('msg', 'Unknown error')}")

        # Return success data
        return result.get("data") if result.get("code") == 0 else None

    @classmethod
    def _should_retry(cls, exception: Exception) -> bool:
        """Determine whether the exception should be retried.
        
        Args:
            exception: The exception to evaluate
            
        Returns:
            True if the request should be retried, False otherwise
        """
        # Network connection related errors
        if isinstance(
            exception, (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError)
        ):
            return True

        # HTTP status code errors
        if isinstance(exception, httpx.HTTPStatusError):
            status_code = exception.response.status_code
            return status_code in [408, 429, 500, 502, 503, 504]

        return False

    @classmethod
    def _execute_request(cls, method: str, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Request executor with retry mechanism.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            **kwargs: Additional arguments for the request
            
        Returns:
            Dictionary containing response data or None if no data
            
        Raises:
            Exception: If the request fails after all retries
        """
        retry_count = 0

        while retry_count <= cls.max_retries:
            try:
                # Execute request
                return cls._request(method, endpoint, **kwargs)
            except Exception as e:
                # Determine whether to retry
                if retry_count < cls.max_retries and cls._should_retry(e):
                    retry_count += 1
                    logger.warning(
                        f"{method} {endpoint} request failed, will retry for the {retry_count} time after {cls.retry_delay:.1f} seconds"
                    )
                    time.sleep(cls.retry_delay)
                    continue
                else:
                    # Do not retry, throw exception directly
                    logger.error(f"Request failed after {retry_count} retries: {str(e)}")
                    raise

    @classmethod
    def safe_close(cls) -> None:
        """Safely close the connection pool."""
        if cls._client:
            cls._client.close()
            cls._client = None
        cls._instance = None


def get_server_config() -> Optional[Dict[str, Any]]:
    """Get server basic configuration.
    
    Returns:
        Dictionary containing server configuration or None if failed
    """
    if not ManageApiClient._instance:
        logger.error("ManageApiClient not initialized")
        return None
    return ManageApiClient._instance._execute_request("POST", "/config/server-base")


def get_agent_models(
    mac_address: str, client_id: str, selected_module: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Get agent model configuration.
    
    Args:
        mac_address: MAC address of the device
        client_id: Client identifier
        selected_module: Dictionary of selected modules
        
    Returns:
        Dictionary containing agent model configuration or None if failed
    """
    if not ManageApiClient._instance:
        logger.error("ManageApiClient not initialized")
        return None
    return ManageApiClient._instance._execute_request(
        "POST",
        "/config/agent-models",
        json={
            "macAddress": mac_address,
            "clientId": client_id,
            "selectedModule": selected_module,
        },
    )


def save_mem_local_short(mac_address: str, short_memory: str) -> Optional[Dict[str, Any]]:
    """Save short-term memory to server.
    
    Args:
        mac_address: MAC address of the device
        short_memory: Short-term memory content to save
        
    Returns:
        Dictionary containing response data or None if failed
    """
    try:
        if not ManageApiClient._instance:
            logger.error("ManageApiClient not initialized")
            return None
        return ManageApiClient._instance._execute_request(
            "PUT",
            f"/agent/saveMemory/{mac_address}",
            json={
                "summaryMemory": short_memory,
            },
        )
    except Exception as e:
        logger.error(f"Failed to save short-term memory to server: {e}")
        return None


def report(
    mac_address: str, 
    session_id: str, 
    chat_type: int, 
    content: str, 
    audio: Optional[bytes], 
    report_time: str
) -> Optional[Dict[str, Any]]:
    """Report chat history with optional audio data.
    
    Args:
        mac_address: MAC address of the device
        session_id: Session identifier
        chat_type: Type of chat
        content: Chat content
        audio: Optional audio data in bytes
        report_time: Timestamp of the report
        
    Returns:
        Dictionary containing response data or None if failed
    """
    if not content or not ManageApiClient._instance:
        return None
    try:
        return ManageApiClient._instance._execute_request(
            "POST",
            "/agent/chat-history/report",
            json={
                "macAddress": mac_address,
                "sessionId": session_id,
                "chatType": chat_type,
                "content": content,
                "reportTime": report_time,
                "audioBase64": (
                    base64.b64encode(audio).decode("utf-8") if audio else None
                ),
            },
        )
    except Exception as e:
        logger.error(f"TTS report failed: {e}")
        return None


def init_service(config: Dict[str, Any]) -> None:
    """Initialize the API client service.
    
    Args:
        config: Configuration dictionary for the API client
    """
    ManageApiClient(config)


def manage_api_http_safe_close() -> None:
    """Safely close the API client connection."""
    ManageApiClient.safe_close()
