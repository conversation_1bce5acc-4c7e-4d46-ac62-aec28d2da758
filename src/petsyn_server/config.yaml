server:
  # Server listening address and port
  ip: 0.0.0.0
  port: 8000
  http_port: 8003
  auth:
    enabled: false
    # Device tokens, which can be written with your custom-defined tokens during firmware compilation
    # The firmware token must match one of the tokens below to connect to this server
    tokens:
      - token: "your-token1" # Token for device 1
        name: "your-device-name1"  # Identifier for device 1
      - token: "your-token2"  # Token for device 2
        name: "your-device-name2" # Identifier for device 2
    # Optional: Device whitelist. If a whitelist is set, machines on the whitelist can connect regardless of their token.
    #allowed_devices:
    #  - "24:0A:C4:1D:3B:F0"  # List of MAC addresses


selected_module:
  LLM: ChatGLMLLM


LLM:
  ChatGLMLLM:
    type: openai
    # api key https://bigmodel.cn/usercenter/proj-mgmt/apikeys
    model_name: glm-4-flash # free 
    url: https://open.bigmodel.cn/api/paas/v4/
    api_key: your chat-glm web key

log:
  # Set console log format: time, log level, tag, message
  log_format: "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>"
  # Set log file output format: time, log level, tag, message
  log_format_file: "{time:YYYY-MM-DD HH:mm:ss} - {version}_{selected_module} - {name} - {level} - {extra[tag]} - {message}"
  # Set log level: INFO, DEBUG
  log_level: INFO
  # Set log directory path
  log_dir: tmp
  # Set log file name
  log_file: "server.log"
  # Set data file directory path
  data_dir: data