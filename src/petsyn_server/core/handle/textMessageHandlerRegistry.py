from typing import Dict, Optional, List

from petsyn_server.core.handle.textHandler.abortMessageHandler import AbortTextMessageHandler
from petsyn_server.core.handle.textHandler.helloMessageHandler import HelloTextMessageHandler
from petsyn_server.core.handle.textHandler.iotMessageHandler import IotTextMessageHandler
from petsyn_server.core.handle.textHandler.listenMessageHandler import ListenTextMessageHandler
from petsyn_server.core.handle.textHandler.mcpMessageHandler import McpTextMessageHandler
from petsyn_server.core.handle.textMessageHandler import TextMessageHandler
from petsyn_server.core.handle.textHandler.serverMessageHandler import ServerTextMessageHandler

TAG = __name__


class TextMessageHandlerRegistry:
    """Registry for managing text message handlers."""
    
    def __init__(self) -> None:
        """Initialize the registry with default handlers."""
        self._handlers: Dict[str, TextMessageHandler] = {}
        self._register_default_handlers()

    def _register_default_handlers(self) -> None:
        """Register all default message handlers."""
        handlers: List[TextMessageHandler] = [
            HelloTextMessageHandler(),
            AbortTextMessageHandler(),
            ListenTextMessageHandler(),
            IotTextMessageHandler(),
            McpTextMessageHandler(),
            ServerTextMessageHandler(),
        ]

        for handler in handlers:
            self.register_handler(handler)

    def register_handler(self, handler: TextMessageHandler) -> None:
        """Register a message handler.
        
        Args:
            handler: The message handler to register
        """
        self._handlers[handler.message_type.value] = handler

    def get_handler(self, message_type: str) -> Optional[TextMessageHandler]:
        """Get a handler for a specific message type.
        
        Args:
            message_type: The type of message to handle
            
        Returns:
            The handler for the specified message type, or None if not found
        """
        return self._handlers.get(message_type)

    def get_supported_types(self) -> List[str]:
        """Get a list of all supported message types.
        
        Returns:
            List of supported message type strings
        """
        return list(self._handlers.keys())
