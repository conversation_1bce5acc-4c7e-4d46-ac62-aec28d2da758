import json
from typing import Any, Dict

from petsyn_server.core.handle.textMessageHandlerRegistry import TextMessageHandlerRegistry

TAG = __name__


class TextMessageProcessor:
    """Main class for processing text messages."""

    def __init__(self, registry: TextMessageHandlerRegistry):
        """Initialize the message processor with a handler registry.
        
        Args:
            registry: Registry containing message handlers
        """
        self.registry = registry

    async def process_message(self, conn: Any, message: str) -> None:
        """Main entry point for processing messages.
        
        Args:
            conn: Connection object with websocket and logger
            message: The message to process
        """
        try:
            # Parse JSON message
            msg_json = json.loads(message)

            # Handle JSON message
            if isinstance(msg_json, dict):
                message_type = msg_json.get("type")

                # Log the received message
                conn.logger.bind(tag=TAG).info(f"Received {message_type} message: {message}")

                # Get and execute handler
                handler = None
                if message_type:
                    handler = self.registry.get_handler(message_type)
                
                if handler:
                    await handler.handle(conn, msg_json)
                else:
                    conn.logger.bind(tag=TAG).error(f"Received unknown message type: {message}")
            # Handle numeric message
            elif isinstance(msg_json, int):
                conn.logger.bind(tag=TAG).info(f"Received numeric message: {message}")
                await conn.websocket.send(message)

        except json.JSONDecodeError:
            # Forward non-JSON message directly
            conn.logger.bind(tag=TAG).error(f"Failed to parse message: {message}")
            await conn.websocket.send(message)
