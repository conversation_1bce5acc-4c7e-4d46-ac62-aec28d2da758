from petsyn_server.core.handle.textMessageHandlerRegistry import TextMessageHandlerRegistry
from petsyn_server.core.handle.textMessageProcessor import TextMessageProcessor

TAG = __name__

message_registry = TextMessageHandlerRegistry()

message_processor = TextMessageProcessor(message_registry)

async def handleTextMessage(conn, message):
    """Handle text message"""
    await message_processor.process_message(conn, message)
