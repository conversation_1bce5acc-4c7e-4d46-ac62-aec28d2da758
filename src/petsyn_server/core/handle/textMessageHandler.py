from abc import abstractmethod, <PERSON>
from typing import Dict, Any, TYPE_CHECKING

from petsyn_server.core.handle.textMessageType import TextMessageType

TAG = __name__


class TextMessageHandler(ABC):
    """Abstract base class for text message handlers."""
    
    @abstractmethod
    async def handle(self, conn: Any, msg_json: Dict[str, Any]) -> None:
        """Abstract method for handling messages.
        
        Args:
            conn: Connection object
            msg_json: Dictionary containing the message data
            
        Returns:
            None
        """
        pass

    @property
    @abstractmethod
    def message_type(self) -> TextMessageType:
        """Return the type of message this handler processes.
        
        Returns:
            TextMessageType: The type of message this handler is responsible for
        """
        pass
