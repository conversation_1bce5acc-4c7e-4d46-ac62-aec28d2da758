import httpx
import openai
from openai.types import CompletionUsage
from typing import Dict, Any, Generator, Optional, List, Tuple
from petsyn_server.config.logger import setup_logging
from petsyn_server.core.utils.util import check_model_key
from petsyn_server.core.providers.llm.base import LL<PERSON>roviderBase

TAG = __name__
logger = setup_logging()


class LLMProvider(LLMProviderBase):
    def __init__(self, config: Dict[str, Any]):
        """Initialize OpenAI LLM provider with configuration.
        
        Args:
            config: Dictionary containing configuration parameters
        """
        self.model_name = config.get("model_name")
        self.api_key = config.get("api_key")
        self.base_url = config.get("base_url", config.get("url"))
        
        # Configure timeout in seconds
        self.timeout = int(config.get("timeout", 300)) or 300

        # Initialize parameters with defaults and type conversion
        self.max_tokens = 500
        self.temperature = 0.7
        self.top_p = 1.0
        self.frequency_penalty = 0.0
        
        param_defaults = {
            "max_tokens": (500, int),
            "temperature": (0.7, lambda x: round(float(x), 1)),
            "top_p": (1.0, lambda x: round(float(x), 1)),
            "frequency_penalty": (0, lambda x: round(float(x), 1)),
        }

        for param, (default, converter) in param_defaults.items():
            value = config.get(param)
            try:
                setattr(
                    self,
                    param,
                    converter(value) if value not in (None, "") else default,
                )
            except (ValueError, TypeError):
                setattr(self, param, default)

        logger.debug(
            f"LLM parameters initialized: {self.temperature}, {self.max_tokens}, "
            f"{self.top_p}, {self.frequency_penalty}"
        )

        model_key_msg = check_model_key("LLM", self.api_key)
        if model_key_msg:
            logger.bind(tag=TAG).error(model_key_msg)
            
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=httpx.Timeout(self.timeout)
        )

    def response(self, session_id: str, dialogue: List[Dict[str, str]], **kwargs: Any) -> Generator[str, None, None]:
        """Generate streaming response from OpenAI.
        
        Args:
            session_id: Unique identifier for the conversation session
            dialogue: List of conversation messages
            **kwargs: Additional parameters for the response generation
            
        Yields:
            Response tokens
        """
        try:
            # Ensure model_name is not None
            if not self.model_name:
                raise ValueError("Model name is required but not configured")
            
            # Build parameters, excluding unsupported ones for o1 models
            params = {
                "model": self.model_name,
                "messages": dialogue,
                "stream": True,
            }
            
            # Only add these parameters for non-o1 models
            if not self.model_name.startswith("o1"):
                params.update({
                    "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                    "temperature": kwargs.get("temperature", self.temperature),
                    "top_p": kwargs.get("top_p", self.top_p),
                    "frequency_penalty": kwargs.get("frequency_penalty", self.frequency_penalty),
                })
            
            responses = self.client.chat.completions.create(**params)

            is_active = True
            for chunk in responses:
                try:
                    delta = chunk.choices[0].delta if getattr(chunk, "choices", None) else None
                    content = ""
                    if delta is not None:
                        content = getattr(delta, "content", "") or ""

                except IndexError:
                    content = ""
                if content:
                    # Handle special tags that might span multiple chunks
                    if "<think>" in content:
                        is_active = False
                        content = content.split("<think>")[0]
                    if "</think>" in content:
                        is_active = True
                        content = content.split("</think>")[-1]
                    if is_active:
                        yield content

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in response generation: {e}")

    def response_with_functions(self, session_id: str, dialogue: List[Dict[str, str]], 
                              functions: Optional[List[Dict]] = None) -> Generator[Tuple[Optional[str], Optional[Any]], None, None]:
        """Generate streaming response with function calling support.
        
        Args:
            session_id: Unique identifier for the conversation session
            dialogue: List of conversation messages
            functions: Optional list of function definitions
            
        Yields:
            Tuples of (content, tool_calls) where either can be None
        """
        try:
            # Ensure model_name is not None for function calls
            if not self.model_name:
                raise ValueError("Model name is required but not configured")
            
            # Build parameters for function calling
            params = {
                "model": self.model_name,
                "messages": dialogue,
                "stream": True,
            }
            
            # Add tools if provided
            if functions:
                params["tools"] = functions
            
            stream = self.client.chat.completions.create(**params)

            for chunk in stream:
                # Check if there are valid choices with content
                if getattr(chunk, "choices", None):
                    yield chunk.choices[0].delta.content, chunk.choices[0].delta.tool_calls
                # Log token usage when CompletionUsage message is received
                elif isinstance(getattr(chunk, "usage", None), CompletionUsage):
                    usage_info = getattr(chunk, "usage", None)
                    logger.bind(tag=TAG).info(
                        f"Token usage - Input: {getattr(usage_info, 'prompt_tokens', 'unknown')}, "
                        f"Output: {getattr(usage_info, 'completion_tokens', 'unknown')}, "
                        f"Total: {getattr(usage_info, 'total_tokens', 'unknown')}"
                    )

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in function call streaming: {e}")
            yield f"[LLM Service Error: {e}]", None



