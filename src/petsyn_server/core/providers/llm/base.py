from abc import ABC, abstractmethod
from typing import Generator, List, Dict, Any, Optional, Tuple
from petsyn_server.config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class LLMProviderBase(ABC):
    @abstractmethod
    def response(self, session_id: str, dialogue: List[Dict[str, str]]) -> Generator[str, None, None]:
        """
        Abstract method for generating LLM responses.
        
        Args:
            session_id: Unique identifier for the conversation session
            dialogue: List of conversation messages in dictionary format
            
        Returns:
            Generator that yields response tokens
        """
        pass

    def response_no_stream(self, system_prompt: str, user_prompt: str, **kwargs: Any) -> str:
        """
        Generate a complete non-streaming response.
        
        Args:
            system_prompt: System level instruction for the LLM
            user_prompt: User's input prompt
            **kwargs: Additional arguments for response generation
            
        Returns:
            Complete response as a string
        """
        try:
            # Construct dialogue format
            dialogue = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = ""
            for part in self.response("", dialogue, **kwargs):
                result += part
            return result

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in LLM response generation: {str(e)}")
            return "[LLM Service Error]"
    
    def response_with_functions(self, session_id: str, dialogue: List[Dict[str, str]], 
                              functions: Optional[List[Dict]] = None) -> Generator[Tuple[str, Optional[Dict]], None, None]:
        """
        Default implementation for function calling with streaming response.
        
        Args:
            session_id: Unique identifier for the conversation session
            dialogue: List of conversation messages in dictionary format
            functions: Optional list of function definitions
            
        Returns:
            Generator yielding tuples of (token, function_call)
            where function_call is None for regular tokens
        """
        # For providers that don't support functions, just return regular response
        for token in self.response(session_id, dialogue):
            yield token, None
