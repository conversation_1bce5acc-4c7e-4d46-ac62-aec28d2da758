import openai
import json
from typing import Dict, Any, Callable, <PERSON><PERSON>, Union
from petsyn_server.config.logger import setup_logging
from petsyn_server.core.utils.util import check_model_key
from petsyn_server.core.providers.vllm.base import VLLMProviderBase

TAG = __name__
logger = setup_logging()


class VLLMProvider(VLLMProviderBase):
    """OpenAI VLLM provider for vision and language model services."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the OpenAI VLLM provider with configuration.
        
        Args:
            config: Configuration dictionary containing model settings
        """
        self.model_name = config.get("model_name")
        self.api_key = config.get("api_key")
        # Use base_url if available, otherwise fall back to url
        self.base_url = config.get("base_url", config.get("url"))

        # Define parameter defaults with their conversion functions
        param_defaults: Dict[str, Tuple[Any, Callable]] = {
            "max_tokens": (500, int),
            "temperature": (0.7, lambda x: round(float(x), 1)),
            "top_p": (1.0, lambda x: round(float(x), 1)),
        }

        # Apply configuration parameters with proper type conversion
        for param, (default, converter) in param_defaults.items():
            value = config.get(param)
            try:
                setattr(
                    self,
                    param,
                    converter(value) if value not in (None, "") else default,
                )
            except (ValueError, TypeError):
                setattr(self, param, default)

        # Validate API key
        model_key_msg = check_model_key("VLLM", self.api_key)
        if model_key_msg:
            logger.bind(tag=TAG).error(model_key_msg)
            
        # Initialize OpenAI client
        self.client = openai.OpenAI(api_key=self.api_key, base_url=self.base_url)

    def response(self, question: str, base64_image: str) -> str:
        """Generate a response to a question about an image.
        
        Args:
            question: The question to ask about the image
            base64_image: Base64-encoded image data
            
        Returns:
            The generated response text
            
        Raises:
            Exception: If there's an error generating the response
        """
        # Append Chinese language instruction to the question
        question = question + "(Please respond in Chinese)"
        
        try:
            # Construct the message with text and image
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            ]

            # Ensure model_name is not None
            if not self.model_name:
                raise ValueError("Model name is required but not configured")
            
            # Build parameters, excluding unsupported ones for o1 models
            params = {
                "model": self.model_name,
                "messages": messages,
                "stream": False,
            }

            # Generate response using OpenAI API
            response = self.client.chat.completions.create(**params)

            return response.choices[0].message.content

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in response generation: {e}")
            raise
