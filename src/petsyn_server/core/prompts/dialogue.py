import uuid
import re
from typing import List, Dict, Optional, Any
from datetime import datetime


class Message:
    """Represents a single message in a dialogue."""
    
    def __init__(
        self,
        role: str,
        content: Optional[str] = None,
        uniq_id: Optional[str] = None,
        tool_calls: Optional[Any] = None,
        tool_call_id: Optional[str] = None,
    ):
        """Initialize a message.
        
        Args:
            role: The role of the message sender (e.g., "system", "user", "assistant")
            content: The content of the message
            uniq_id: Unique identifier for the message
            tool_calls: Tool calls associated with the message
            tool_call_id: ID for tool response messages
        """
        self.uniq_id = uniq_id if uniq_id is not None else str(uuid.uuid4())
        self.role = role
        self.content = content
        self.tool_calls = tool_calls
        self.tool_call_id = tool_call_id


class Dialogue:
    """Manages a dialogue conversation with multiple messages."""
    
    def __init__(self):
        """Initialize an empty dialogue."""
        self.dialogue: List[Message] = []
        # Get current time
        self.current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def put(self, message: Message) -> None:
        """Add a message to the dialogue.
        
        Args:
            message: The message to add
        """
        self.dialogue.append(message)

    def get_messages(self, message: Message, dialogue_list: List[Dict]) -> None:
        """Convert a Message object to a dictionary format and add to dialogue list.
        
        Args:
            message: The message to convert
            dialogue_list: The list to add the formatted message to
        """
        if message.tool_calls is not None:
            dialogue_list.append({"role": message.role, "tool_calls": message.tool_calls})
        elif message.role == "tool":
            dialogue_list.append(
                {
                    "role": message.role,
                    "tool_call_id": (
                        str(uuid.uuid4()) if message.tool_call_id is None else message.tool_call_id
                    ),
                    "content": message.content,
                }
            )
        else:
            dialogue_list.append({"role": message.role, "content": message.content})

    def get_llm_dialogue(self) -> List[Dict[str, str]]:
        """Get the dialogue in a format suitable for LLM processing.
        
        Returns:
            List of message dictionaries formatted for LLM consumption
        """
        # Directly call get_llm_dialogue_with_memory with None as memory_str
        # This ensures speaker functionality works in all call paths
        return self.get_llm_dialogue_with_memory(None, None)

    def update_system_message(self, new_content: str) -> None:
        """Update or add a system message.
        
        Args:
            new_content: The content for the system message
        """
        # Find the first system message
        system_msg = next((msg for msg in self.dialogue if msg.role == "system"), None)
        if system_msg:
            system_msg.content = new_content
        else:
            self.put(Message(role="system", content=new_content))

    def get_llm_dialogue_with_memory(
        self, memory_str: Optional[str] = None, voiceprint_config: Optional[Dict] = None
    ) -> List[Dict[str, str]]:
        """Get the dialogue in a format suitable for LLM processing with memory and voiceprint info.
        
        Args:
            memory_str: Optional memory string to include in the system prompt
            voiceprint_config: Optional configuration for voiceprint/speaker information
            
        Returns:
            List of message dictionaries formatted for LLM consumption
        """
        # Build dialogue
        dialogue = []

        # Add system prompt and memory
        system_message = next(
            (msg for msg in self.dialogue if msg.role == "system"), None
        )

        if system_message and system_message.content is not None:
            # Basic system prompt
            enhanced_system_prompt = system_message.content
            # Replace time placeholder
            enhanced_system_prompt = enhanced_system_prompt.replace(
                "{{current_time}}", datetime.now().strftime("%H:%M")
            )


            # Add speaker personalization descriptions
            try:
                if voiceprint_config:
                    speakers = voiceprint_config.get("speakers", [])
                    if speakers:
                        enhanced_system_prompt += "\n\n<speakers_info>"
                        for speaker_str in speakers:
                            try:
                                parts = speaker_str.split(",", 2)
                                if len(parts) >= 2:
                                    name = parts[1].strip()
                                    # If description is empty, use ""
                                    description = (
                                        parts[2].strip() if len(parts) >= 3 else ""
                                    )
                                    enhanced_system_prompt += f"\n- {name}: {description}"
                            except Exception:
                                # Skip malformed speaker entries
                                continue
                        enhanced_system_prompt += "\n\n</speakers_info>"
            except Exception:
                # Ignore configuration reading errors, don't affect other functionality
                pass

            # Use regex to match <memory> tags, regardless of content in between
            if memory_str is not None:
                enhanced_system_prompt = re.sub(
                    r"<memory>.*?</memory>",
                    f"<memory>\n{memory_str}\n</memory>",
                    enhanced_system_prompt,
                    flags=re.DOTALL,
                )
            dialogue.append({"role": "system", "content": enhanced_system_prompt})

        # Add user and assistant messages
        for message in self.dialogue:
            if message.role != "system":  # Skip original system message
                self.get_messages(message, dialogue)

        return dialogue
