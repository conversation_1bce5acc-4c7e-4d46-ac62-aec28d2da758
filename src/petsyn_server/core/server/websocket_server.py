import asyncio
import websockets
from typing import Dict, Any, Set
from petsyn_server.config.logger import setup_logging
from petsyn_server.core.server.connection import ConnectionHandler
from petsyn_server.config.config_loader import get_config_from_api
from petsyn_server.core.utils.modules_initialize import initialize_modules

TAG = __name__


class WebSocketServer:
    def __init__(self, config: Dict[str, Any]):
        """Initialize the WebSocket server with configuration.
        
        Args:
            config: Server configuration dictionary
        """
        self.config = config
        self.logger = setup_logging()
        self.config_lock = asyncio.Lock()
        
        # Initialize modules based on configuration
        modules = initialize_modules(
            logger=self.logger,
            config=self.config,
            init_vad="VAD" in self.config["selected_module"],
            init_asr="ASR" in self.config["selected_module"],
            init_llm="LLM" in self.config["selected_module"],
            init_tts=False,
            init_memory="Memory" in self.config["selected_module"],
            init_intent="Intent" in self.config["selected_module"],
        )
        self._vad = modules["vad"] if "vad" in modules else None
        self._asr = modules["asr"] if "asr" in modules else None
        self._llm = modules["llm"] if "llm" in modules else None
        self._intent = modules["intent"] if "intent" in modules else None
        self._memory = modules["memory"] if "memory" in modules else None
        
        # Track active connections
        self.active_connections: Set[ConnectionHandler] = set()

    async def start(self):
        """Start the WebSocket server."""
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("port", 8000))

        async with websockets.serve(
            self._handle_connection, host, port, process_request=self._http_response
        ):
            # Keep the server running indefinitely
            await asyncio.Future()

    async def _handle_connection(self, websocket):
        """Handle new WebSocket connections by creating a dedicated ConnectionHandler.
        
        Args:
            websocket: The WebSocket connection object
        """
        # Create a new ConnectionHandler for this connection
        handler = ConnectionHandler(
            self.config,
            self._vad,
            self._asr,
            self._llm,
            self._memory,
            self._intent,
            self,  # Pass the server instance
        )
        self.active_connections.add(handler)
        
        try:
            await handler.handle_connection(websocket)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error handling connection: {e}")
        finally:
            # Ensure the handler is removed from active connections
            self.active_connections.discard(handler)
            # Force close the connection if it's not already closed
            await self._safe_close_websocket(websocket)

    async def _safe_close_websocket(self, websocket):
        """Safely close a WebSocket connection.
        
        Args:
            websocket: The WebSocket connection to close
        """
        try:
            # Check different ways to determine if the websocket is closed
            if hasattr(websocket, "closed") and not websocket.closed:
                await websocket.close()
            elif hasattr(websocket, "state") and websocket.state.name != "CLOSED":
                await websocket.close()
            else:
                # If no clear status, just attempt to close
                await websocket.close()
        except Exception as close_error:
            self.logger.bind(tag=TAG).error(
                f"Error closing WebSocket connection: {close_error}"
            )

    async def _http_response(self, websocket, request_headers):
        """Handle HTTP requests that aren't WebSocket upgrades.
        
        Args:
            websocket: The WebSocket connection object
            request_headers: The HTTP request headers
            
        Returns:
            Response for non-WebSocket requests or None to continue with WebSocket handshake
        """
        # Check if this is a WebSocket upgrade request
        if request_headers.headers.get("connection", "").lower() == "upgrade":
            # If it's a WebSocket request, return None to allow the handshake to continue
            return None
        else:
            # If it's a regular HTTP request, return a simple response
            return websocket.respond(200, "Server is running\n")

    async def update_config(self) -> bool:
        """Update server configuration and reinitialize components.

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            async with self.config_lock:
                # Fetch new configuration
                new_config = get_config_from_api(self.config)
                if new_config is None:
                    self.logger.bind(tag=TAG).error("Failed to fetch new configuration")
                    return False
                
                self.logger.bind(tag=TAG).info("Successfully fetched new configuration")

                # Update configuration
                self.config = new_config
                
                # Reinitialize modules with new configuration
                modules = initialize_modules(
                    logger=self.logger,
                    config=new_config, # TODO asr
                    init_llm="LLM" in new_config["selected_module"],
                    init_memory="Memory" in new_config["selected_module"],
                    init_intent="Intent" in new_config["selected_module"],
                )

                # Update modules
                if "llm" in modules:
                    self._llm = modules["llm"]
                if "intent" in modules:
                    self._intent = modules["intent"]
                if "memory" in modules:
                    self._memory = modules["memory"]
                    
                self.logger.bind(tag=TAG).info("Configuration update completed successfully")
                return True
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Failed to update server configuration: {str(e)}")
            return False
