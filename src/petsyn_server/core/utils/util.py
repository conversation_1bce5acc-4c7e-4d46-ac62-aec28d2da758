import copy
import socket
import subprocess
import re

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Connect to Google's DNS servers
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        return "127.0.0.1"

def get_vision_url(config: dict) -> str:
    """获取 vision URL

    Args:
        config: 配置字典

    Returns:
        str: vision URL
    """
    server_config = config["server"]
    vision_explain = server_config.get("vision_explain", "")
    if "你的" in vision_explain:
        local_ip = get_local_ip()
        port = int(server_config.get("http_port", 8003))
        vision_explain = f"http://{local_ip}:{port}/mcp/vision/explain"
    return vision_explain

def is_valid_image_file(file_data: bytes) -> bool:
    """
    检查文件数据是否为有效的图片格式

    Args:
        file_data: 文件的二进制数据

    Returns:
        bool: 如果是有效的图片格式返回True，否则返回False
    """
    # 常见图片格式的魔数（文件头）
    image_signatures = {
        b"\xff\xd8\xff": "JPEG",
        b"\x89PNG\r\n\x1a\n": "PNG",
        b"GIF87a": "GIF",
        b"GIF89a": "GIF",
        b"BM": "BMP",
        b"II*\x00": "TIFF",
        b"MM\x00*": "TIFF",
        b"RIFF": "WEBP",
    }

    # 检查文件头是否匹配任何已知的图片格式
    for signature in image_signatures:
        if file_data.startswith(signature):
            return True

    return False

def check_model_key(model_type, model_key):
    """
    Check if the model API key is properly configured.
    
    Args:
        model_type (str): Type of the model
        model_key (str): API key for the model
        
    Returns:
        str or None: Error message if key is not properly set, None otherwise
    """
    if "your" in model_key:
        return f"Configuration error: API key for {model_type} is not set, current value: {model_key}"
    return None


def check_ffmpeg_installed():
    """
    Check if FFmpeg is properly installed on the system.
    
    Returns:
        bool: True if FFmpeg is installed, False otherwise
        
    Raises:
        ValueError: If FFmpeg is not properly installed
    """
    try:
        # Run ffmpeg -version to check if it's installed
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True,
        )
        output = result.stdout + result.stderr
        return "ffmpeg version" in output.lower()
    except (subprocess.CalledProcessError, FileNotFoundError):
        error_msg = "FFmpeg is not properly installed on your computer\n"
        error_msg += "\nPlease:\n"
        error_msg += "1. Follow the project installation documentation to properly install FFmpeg\n"
        error_msg += "2. Make sure FFmpeg is added to your system's PATH\n"
        raise ValueError(error_msg)


def extract_json_from_string(input_string):
    """
    Extract JSON object from a string.

    Args:
        text (str): String that may contain JSON

    Returns:
        dict or None: Parsed JSON object if found, None otherwise
    """
    pattern = r"(\{.*\})"
    match = re.search(pattern, input_string, re.DOTALL)
    if match:
        return match.group(1)
    return None


def check_vad_update(common_config, private_config):
    """
    Check if VAD (Voice Activity Detection) needs to be updated.

    Args:
        common_config (dict): Common configuration
        private_config (dict): Private configuration

    Returns:
        bool: True if VAD needs update, False otherwise
    """
    # TODO：Placeholder implementation - return False for now
    return False


def check_asr_update(common_config, private_config):
    """
    Check if ASR (Automatic Speech Recognition) needs to be updated.

    Args:
        common_config (dict): Common configuration
        private_config (dict): Private configuration

    Returns:
        bool: True if ASR needs update, False otherwise
    """
    # TODO：Placeholder implementation - return False for now
    return False


def filter_sensitive_info(config):
    """
    Filter sensitive information from configuration for logging.

    Args:
        config (dict): Configuration dictionary

    Returns:
        dict: Configuration with sensitive info filtered
    """
    sensitive_keys = [
        "api_key",
        "personal_access_token",
        "access_token",
        "token",
        "secret",
        "access_key_secret",
        "secret_key",
    ]

    def _filter_dict(d: dict) -> dict:
        filtered = {}
        for k, v in d.items():
            if any(sensitive in k.lower() for sensitive in sensitive_keys):
                filtered[k] = "***"
            elif isinstance(v, dict):
                filtered[k] = _filter_dict(v)
            elif isinstance(v, list):
                filtered[k] = [_filter_dict(i) if isinstance(i, dict) else i for i in v]
            else:
                filtered[k] = v
        return filtered

    return _filter_dict(copy.deepcopy(config))
