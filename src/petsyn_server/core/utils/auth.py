import jwt
import time
import json
import os
from typing import Tuple, Optional
from datetime import datetime, timedelta, timezone

from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import base64

from typing import Dict, Any, Set, Optional
from petsyn_server.config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class AuthenticationError(Exception):
    """Exception raised for authentication failures."""
    pass


class AuthMiddleware:
    """Middleware for handling authentication of incoming requests."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the authentication middleware.
        
        Args:
            config: Configuration dictionary containing authentication settings
        """
        self.config = config
        self.auth_config = config["server"].get("auth", {})
        # Build token lookup table
        self.tokens: Dict[str, str] = {
            item["token"]: item["name"]
            for item in self.auth_config.get("tokens", [])
        }
        # Device whitelist
        self.allowed_devices: Set[str] = set(
            self.auth_config.get("allowed_devices", [])
        )

    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """Authenticate the connection request.
        
        Args:
            headers: Dictionary of request headers
            
        Returns:
            True if authentication is successful
            
        Raises:
            AuthenticationError: If authentication fails
        """
        # Check if authentication is enabled
        if not self.auth_config.get("enabled", False):
            return True

        # Check if device is in whitelist
        device_id = headers.get("device-id", "")

        if self.allowed_devices and device_id in self.allowed_devices:
            return True

        # Validate Authorization header
        auth_header = headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            logger.bind(tag=TAG).error("Missing or invalid Authorization header")
            raise AuthenticationError("Missing or invalid Authorization header")

        token = auth_header.split(" ")[1]
        if token not in self.tokens:
            logger.bind(tag=TAG).error(f"Invalid token: {token}")
            raise AuthenticationError("Invalid token")

        logger.bind(tag=TAG).info(f"Authentication successful - Device: {device_id}, Token: {self.tokens[token]}")
        return True

    def get_token_name(self, token: str) -> Optional[str]:
        """Get the device name associated with a token.
        
        Args:
            token: The authentication token to look up
            
        Returns:
            The device name associated with the token, or None if not found
        """
        return self.tokens.get(token)


class AuthToken:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key.encode()  # Convert to bytes
        # Derive fixed-length encryption key (32 bytes for AES-256)
        self.encryption_key = self._derive_key(32)

    def _derive_key(self, length: int) -> bytes:
        """Derive fixed-length key using PBKDF2"""
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

        # Use fixed salt (should use random salt in production)
        salt = b"fixed_salt_placeholder"  # Should be randomly generated in production
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=length,
            salt=salt,
            iterations=100000,
            backend=default_backend(),
        )
        return kdf.derive(self.secret_key)

    def _encrypt_payload(self, payload: dict) -> str:
        """Encrypt entire payload using AES-GCM"""
        # Convert payload to JSON string
        payload_json = json.dumps(payload)

        # Generate random IV
        iv = os.urandom(12)
        # Create cipher
        cipher = Cipher(
            algorithms.AES(self.encryption_key),
            modes.GCM(iv),
            backend=default_backend(),
        )
        encryptor = cipher.encryptor()

        # Encrypt and generate tag
        ciphertext = encryptor.update(payload_json.encode()) + encryptor.finalize()
        tag = encryptor.tag

        # Combine IV + ciphertext + tag
        encrypted_data = iv + ciphertext + tag
        return base64.urlsafe_b64encode(encrypted_data).decode()

    def _decrypt_payload(self, encrypted_data: str) -> dict:
        """Decrypt AES-GCM encrypted payload"""
        # Decode Base64
        data = base64.urlsafe_b64decode(encrypted_data.encode())
        # Split components
        iv = data[:12]
        tag = data[-16:]
        ciphertext = data[12:-16]

        # Create decryptor
        cipher = Cipher(
            algorithms.AES(self.encryption_key),
            modes.GCM(iv, tag),
            backend=default_backend(),
        )
        decryptor = cipher.decryptor()

        # Decrypt
        plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        return json.loads(plaintext.decode())

    def generate_token(self, device_id: str) -> str:
        """Generate JWT token
        
        Args:
            device_id: Device ID
            
        Returns:
            JWT token string
        """
        # Set expiration time to 1 hour from now
        expire_time = datetime.now(timezone.utc) + timedelta(hours=1)

        # Create original payload
        payload = {"device_id": device_id, "exp": expire_time.timestamp()}

        # Encrypt entire payload
        encrypted_payload = self._encrypt_payload(payload)

        # Create outer payload containing encrypted data
        outer_payload = {"data": encrypted_payload}

        # Encode using JWT
        token = jwt.encode(outer_payload, self.secret_key, algorithm="HS256")
        return token

    def verify_token(self, token: str) -> Tuple[bool, Optional[str]]:
        """Verify token
        
        Args:
            token: JWT token string
            
        Returns:
            Tuple of (is_valid, device_id)
        """
        try:
            # First verify outer JWT (signature and expiration)
            outer_payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])

            # Decrypt inner payload
            inner_payload = self._decrypt_payload(outer_payload["data"])

            # Double-check expiration time (double verification)
            if inner_payload["exp"] < time.time():
                return False, None

            return True, inner_payload["device_id"]

        except jwt.InvalidTokenError:
            return False, None
        except json.JSONDecodeError:
            return False, None
        except Exception as e:
            logger.bind(tag=TAG).error(f"Token verification failed: {str(e)}")
            return False, None
