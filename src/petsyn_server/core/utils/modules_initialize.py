from typing import Dict, Any
from petsyn_server.config.logger import setup_logging
from petsyn_server.core.utils.deploy import llm, intent, memory

TAG = __name__
logger = setup_logging()


def initialize_modules(
    logger,
    config: Dict[str, Any],
    init_vad=False, #TODO
    init_asr=False,
    init_llm=False,
    init_tts=False,
    init_memory=False,
    init_intent=False,
) -> Dict[str, Any]:
    """
    Initialize all module components.

    Args:
        logger: Logger instance for logging messages
        config: Configuration dictionary containing module settings
        init_llm: Flag to determine whether to initialize LLM module

    Returns:
        Dict[str, Any]: Dictionary containing all initialized modules
    """
    modules = {}

    # Initialize LLM module if requested
    if init_llm:
        selected_llm_module = config["selected_module"]["LLM"]
        llm_config = config["LLM"][selected_llm_module]
        
        # Determine LLM type - use module name as type if not explicitly specified
        llm_type = selected_llm_module if "type" not in llm_config else llm_config["type"]
        
        # Create and store LLM instance
        modules["llm"] = llm.create_instance(llm_type, llm_config)
        logger.bind(tag=TAG).info(f"Successfully initialized LLM module: {selected_llm_module}")
    
    # 初始化Intent模块
    if init_intent:
        select_intent_module = config["selected_module"]["Intent"]
        intent_type = (
            select_intent_module
            if "type" not in config["Intent"][select_intent_module]
            else config["Intent"][select_intent_module]["type"]
        )
        modules["intent"] = intent.create_instance(
            intent_type,
            config["Intent"][select_intent_module],
        )
        logger.bind(tag=TAG).info(f"初始化组件: intent成功 {select_intent_module}")

    # 初始化Memory模块
    if init_memory:
        select_memory_module = config["selected_module"]["Memory"]
        memory_type = (
            select_memory_module
            if "type" not in config["Memory"][select_memory_module]
            else config["Memory"][select_memory_module]["type"]
        )
        modules["memory"] = memory.create_instance(
            memory_type,
            config["Memory"][select_memory_module],
            config.get("summaryMemory", None),
        )
        logger.bind(tag=TAG).info(f"初始化组件: memory成功 {select_memory_module}")


    return modules