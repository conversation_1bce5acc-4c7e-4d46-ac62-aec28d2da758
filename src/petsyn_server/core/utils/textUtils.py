import json
from typing import Any, Set, <PERSON><PERSON>, List

TAG = __name__

EMOJI_List = [
    "😶",
    "🙂",
    "😆",
    "😂",
    "😔",
    "😠",
    "😭",
    "😍",
    "😳",
    "😲",
    "😱",
    "🤔",
    "😉",
    "😎",
    "😌",
    "🤤",
    "😘",
    "😏",
    "😴",
    "😜",
    "🙄",
]

WEEKDAY_MAP = {
    "Monday": "星期一",
    "Tuesday": "星期二",
    "Wednesday": "星期三",
    "Thursday": "星期四",
    "Friday": "星期五",
    "Saturday": "星期六",
    "Sunday": "星期日",
}

EMOJI_MAP = {
    "😂": "laughing",
    "😭": "crying",
    "😠": "angry",
    "😔": "sad",
    "😍": "loving",
    "😲": "surprised",
    "😱": "shocked",
    "🤔": "thinking",
    "😌": "relaxed",
    "😴": "sleepy",
    "😜": "silly",
    "🙄": "confused",
    "😶": "neutral",
    "🙂": "happy",
    "😆": "laughing",
    "😳": "embarrassed",
    "😉": "winking",
    "😎": "cool",
    "🤤": "delicious",
    "😘": "kissy",
    "😏": "confident",
}
EMOJI_RANGES: List[Tuple[int, int]] = [
    (0x1F600, 0x1F64F),
    (0x1F300, 0x1F5FF),
    (0x1F680, 0x1F6FF),
    (0x1F900, 0x1F9FF),
    (0x1FA70, 0x1FAFF),
    (0x2600, 0x26FF),
    (0x2700, 0x27BF),
]


def get_string_no_punctuation_or_emoji(s: str) -> str:
    """Remove spaces, punctuation, and emoji characters from the beginning and end of a string.
    
    Args:
        s: The input string to process
        
    Returns:
        The processed string with leading and trailing punctuation/emojis removed
    """
    chars = list(s)
    # Process characters from the start
    start = 0
    while start < len(chars) and is_punctuation_or_emoji(chars[start]):
        start += 1
    # Process characters from the end
    end = len(chars) - 1
    while end >= start and is_punctuation_or_emoji(chars[end]):
        end -= 1
    return "".join(chars[start : end + 1])


def is_punctuation_or_emoji(char: str) -> bool:
    """Check if a character is a space, specified punctuation, or emoji.
    
    Args:
        char: The character to check
        
    Returns:
        True if the character is punctuation or emoji, False otherwise
    """
    # Define punctuation marks to remove (including full-width/half-width)
    punctuation_set: Set[str] = {
        "，",
        ",",  # Chinese comma + English comma
        "。",
        ".",  # Chinese period + English period
        "！",
        "!",  # Chinese exclamation + English exclamation
        "“",
        "”",
        '"',  # Chinese double quotes + English quotes
        "：",
        ":",  # Chinese colon + English colon
        "-",
        "－",  # English hyphen + Chinese full-width hyphen
        "、",  # Chinese enumeration comma
        "[",
        "]",  # Square brackets
        "【",
        "】",  # Chinese square brackets
    }
    if char.isspace() or char in punctuation_set:
        return True
    return is_emoji(char)


async def get_emotion(conn: Any, text: str) -> None:
    """Extract and send emotion information from text.
    
    Args:
        conn: Connection object with websocket and logger
        text: The text to analyze for emotions
    """
    emoji = "🙂"
    emotion = "happy"
    for char in text:
        if char in EMOJI_MAP:
            emoji = char
            emotion = EMOJI_MAP[char]
            break
    try:
        await conn.websocket.send(
            json.dumps(
                {
                    "type": "llm",
                    "text": emoji,
                    "emotion": emotion,
                    "session_id": conn.session_id,
                }
            )
        )
    except Exception as e:
        conn.logger.bind(tag=TAG).warning(f"Failed to send emotion emoji, error: {e}")


def is_emoji(char: str) -> bool:
    """Check if a character is an emoji.
    
    Args:
        char: The character to check
        
    Returns:
        True if the character is an emoji, False otherwise
    """
    code_point = ord(char)
    return any(start <= code_point <= end for start, end in EMOJI_RANGES)


def check_emoji(text: str) -> str:
    """Remove all emoji characters from text.
    
    Args:
        text: The text to process
        
    Returns:
        The text with all emoji characters removed
    """
    return ''.join(char for char in text if not is_emoji(char) and char != "\n")
