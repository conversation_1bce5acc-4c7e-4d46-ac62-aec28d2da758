import os
from pathlib import Path
import sys
from typing import Any

from petsyn_server.config.logger import setup_logging
import importlib

logger = setup_logging()


def create_instance(class_name: str, *args, **kwargs) -> Any:
    """Create an instance of the specified VLLM provider.
    
    Args:
        class_name: Name of the VLLM provider class to instantiate
        *args: Positional arguments to pass to the constructor
        **kwargs: Keyword arguments to pass to the constructor
        
    Returns:
        An instance of the specified VLLM provider
        
    Raises:
        ValueError: If the specified VLLM provider is not supported
    """
    # Check if the provider file exists
    provider_path = Path('src/petsyn_server/core') / 'providers' / 'vllm' / f'{class_name}.py'
    
    if os.path.exists(provider_path):
        # Construct the module name
        lib_name = f"petsyn_server.core.providers.vllm.{class_name}"
        try:
            module = importlib.import_module(lib_name)
            return module.VLLMProvider(*args, **kwargs)
        except ImportError as e:
            logger.error(f"Failed to import module {lib_name}: {str(e)}")
            raise
    # Raise an error if the provider doesn't exist
    raise ValueError(f"Unsupported VLLM type: {class_name}. Please check if the 'type' in configuration is set correctly")
