import os
import sys
from typing import Any
from petsyn_server.config.logger import setup_logging
import importlib

logger = setup_logging()


def create_instance(class_name: str, *args, **kwargs) -> Any:
    """Create an instance of the specified intent provider.
    
    Args:
        class_name: Name of the intent provider class to instantiate
        *args: Positional arguments to pass to the constructor
        **kwargs: Keyword arguments to pass to the constructor
        
    Returns:
        An instance of the specified intent provider
        
    Raises:
        ValueError: If the specified intent provider is not supported
    """
    # Construct the file path to check if the provider exists
    provider_path = os.path.join('core', 'providers', 'intent', class_name, f'{class_name}.py')
    
    if os.path.exists(provider_path):
        # Construct the module name
        lib_name = f'core.providers.intent.{class_name}.{class_name}'
        
        # Import the module if not already imported
        if lib_name not in sys.modules:
            sys.modules[lib_name] = importlib.import_module(lib_name)
        
        # Create and return an instance of the IntentProvider class
        return sys.modules[lib_name].IntentProvider(*args, **kwargs)

    # Raise an error if the provider doesn't exist
    raise ValueError(f"Unsupported intent type: {class_name}. Please check if the 'type' in configuration is set correctly")
