import os
import sys
from pathlib import Path
from petsyn_server.config.logger import setup_logging
import importlib
from typing import Any

logger = setup_logging()


def create_instance(class_name: str, *args: Any, **kwargs: Any) -> Any:
    """
    Create an instance of LLMProvider for the specified class name.
    
    Args:
        class_name: Name of the LLM provider class
        *args: Variable length argument list for the class constructor
        **kwargs: Arbitrary keyword arguments for the class constructor
        
    Returns:
        An instance of the requested LLM provider
        
    Raises:
        ValueError: If the specified LLM type is not supported
    """
    provider_path = Path('src/petsyn_server/core') / 'providers' / 'llm' / class_name / f'{class_name}.py'

    if provider_path.exists():
        lib_name = f'petsyn_server.core.providers.llm.{class_name}.{class_name}'
        try:
            module = importlib.import_module(lib_name)
            return module.LLMProvider(*args, **kwargs)
        except ImportError as e:
            logger.error(f"Failed to import module {lib_name}: {str(e)}")
            raise

    raise ValueError(f"Unsupported LLM type: {class_name}. Please check if the type is configured correctly.")
