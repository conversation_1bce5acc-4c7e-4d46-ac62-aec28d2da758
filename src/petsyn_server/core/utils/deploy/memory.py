import os
import sys
import importlib
from typing import Any
from petsyn_server.config.logger import setup_logging

logger = setup_logging()


def create_instance(class_name: str, *args, **kwargs) -> Any:
    """Create an instance of the specified memory provider.
    
    Args:
        class_name: Name of the memory provider class to instantiate
        *args: Positional arguments to pass to the constructor
        **kwargs: Keyword arguments to pass to the constructor
        
    Returns:
        An instance of the specified memory provider
        
    Raises:
        ValueError: If the specified memory provider is not supported
    """
    # Construct the file path to check if the provider exists
    provider_path = os.path.join("core", "providers", "memory", class_name, f"{class_name}.py")
    
    if os.path.exists(provider_path):
        # Construct the module name
        lib_name = f"core.providers.memory.{class_name}.{class_name}"
        
        # Import the module if not already imported
        if lib_name not in sys.modules:
            sys.modules[lib_name] = importlib.import_module(lib_name)
        
        # Create and return an instance of the MemoryProvider class
        return sys.modules[lib_name].MemoryProvider(*args, **kwargs)

    # Raise an error if the provider doesn't exist
    raise ValueError(f"Unsupported memory service type: {class_name}. Please check if the provider is available.")
