"""
Time utility module
Provides unified time retrieval functionality
"""

import cnlunar
from datetime import datetime
from typing import Tuple

WEEKDAY_MAP = {
    "Monday": "星期一",
    "Tuesday": "星期二", 
    "Wednesday": "星期三",
    "Thursday": "星期四",
    "Friday": "星期五",
    "Saturday": "星期六",
    "Sunday": "星期日",
}


def get_current_time() -> str:
    """
    Get current time string (format: HH:MM).
    
    Returns:
        Current time as a string in HH:MM format
    """
    return datetime.now().strftime("%H:%M")


def get_current_date() -> str:
    """
    Get today's date string (format: YYYY-MM-DD).
    
    Returns:
        Current date as a string in YYYY-MM-DD format
    """
    return datetime.now().strftime("%Y-%m-%d")


def get_current_weekday() -> str:
    """
    Get today's weekday in Chinese.
    
    Returns:
        Current weekday as a Chinese string
    """
    now = datetime.now()
    return WEEKDAY_MAP[now.strftime("%A")]


def get_current_lunar_date() -> str:
    """
    Get lunar date string.
    
    Returns:
        Lunar date as a string or error message if retrieval fails
    """
    try:
        now = datetime.now()
        today_lunar = cnlunar.Lunar(now, godType="8char")
        return f"{today_lunar.lunarYearCn}年{today_lunar.lunarMonthCn[:-1]}{today_lunar.lunarDayCn}"
    except Exception:
        return "Failed to get lunar date"


def get_current_time_info() -> Tuple[str, str, str, str]:
    """
    Get current time information.
    
    Returns:
        A tuple containing (current time string, today's date, today's weekday, lunar date)
    """
    current_time = get_current_time()
    today_date = get_current_date()
    today_weekday = get_current_weekday()
    lunar_date = get_current_lunar_date()
    
    return current_time, today_date, today_weekday, lunar_date
