import asyncio
import signal
import sys
import uuid
from petsyn_server.config.config_loader import load_config
from petsyn_server.config.logger import setup_logging
from petsyn_server.core.utils.util import get_local_ip
from petsyn_server.core.server.http_server import SimpleHttpServer
from petsyn_server.core.utils.util import check_ffmpeg_installed

TAG = __name__
logger = setup_logging()


async def wait_for_exit() -> None:
    """
    Wait for a signal to exit.
    """
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        try:
            await asyncio.Future()
        except KeyboardInterrupt:  # Ctrl‑C
            pass


async def main():
    check_ffmpeg_installed()
    config = load_config()

    auth_key = config.get("manager-api", {}).get("secret", "")
    if not auth_key or len(auth_key) == 0 or "your" in auth_key:
        auth_key = str(uuid.uuid4().hex)
    config["server"]["auth_key"] = auth_key

    logger.bind(tag=TAG).info(f"Server started: {config['server']}")

    http_server = SimpleHttpServer(config)
    http_task = asyncio.create_task(http_server.start())

    port = int(config["server"].get("http_port", 8003))

    logger.bind(tag=TAG).info(
        "Vision port is\thttp://{}:{}/vision/explain",
        get_local_ip(),
        port,
    )
    logger.bind(tag=TAG).info(
        "=============================================================\n"
    )

    try:
        await wait_for_exit()
    except asyncio.CancelledError:
        print("Server stopped with user shut down")
    finally:
        if http_task:
            http_task.cancel()

        await asyncio.wait(
            [http_task] if http_task else [],
            timeout=3.0,
            return_when=asyncio.ALL_COMPLETED,
        )
        logger.bind(tag=TAG).info(
            "Server closed."
        )

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.bind(tag=TAG).info(
            "Server stopped with user shut down"
        )
