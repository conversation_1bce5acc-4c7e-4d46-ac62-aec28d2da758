#!/usr/bin/env python3
"""
Minimal WebSocket Server for PetSyn

This is a simplified version of the websocket server that can run independently
without all the complex dependencies. Use this for testing and development.
"""

import asyncio
import websockets
import json
import logging
from petsyn_server.config.logger import setup_logging
from petsyn_server.config.config_loader import get_config_from_api

TAG = __name__


class MinimalConnectionHandler:
    """Minimal connection handler for testing purposes."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def handle_connection(self, websocket):
        """Handle a WebSocket connection with basic echo functionality."""
        self.logger.bind(tag=TAG).info("New connection established")
        
        try:
            async for message in websocket:
                try:
                    # Try to parse as JSON
                    data = json.loads(message)
                    self.logger.bind(tag=TAG).info(f"Received JSON: {data}")
                    
                    # Echo back with a response
                    response = {
                        "type": "echo",
                        "original": data,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                    await websocket.send(json.dumps(response))
                    
                except json.JSONDecodeError:
                    # Handle plain text messages
                    self.logger.bind(tag=TAG).info(f"Received text: {message}")
                    response = f"Echo: {message}"
                    await websocket.send(response)
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.bind(tag=TAG).info("Connection closed")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error handling connection: {e}")


class MinimalWebSocketServer:
    """Minimal WebSocket server for testing."""
    
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging()
        self.active_connections = set()

    async def start(self):
        """Start the WebSocket server."""
        server_config = self.config.get("server", {})
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("port", 8000))

        self.logger.bind(tag=TAG).info(f"Starting minimal WebSocket server on {host}:{port}")

        async with websockets.serve(
            self._handle_connection, host, port, process_request=self._http_response
        ):
            self.logger.bind(tag=TAG).info("Server started successfully")
            await asyncio.Future()  # Run forever

    async def _handle_connection(self, websocket):
        """Handle new WebSocket connections."""
        handler = MinimalConnectionHandler(self.config, self.logger)
        self.active_connections.add(handler)
        
        try:
            await handler.handle_connection(websocket)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error in connection handler: {e}")
        finally:
            self.active_connections.discard(handler)
            try:
                if not websocket.closed:
                    await websocket.close()
            except Exception as close_error:
                self.logger.bind(tag=TAG).error(f"Error closing connection: {close_error}")

    async def _http_response(self, websocket, request_headers):
        """Handle HTTP requests (non-WebSocket)."""
        if request_headers.headers.get("connection", "").lower() == "upgrade":
            return None  # Allow WebSocket upgrade
        else:
            return websocket.respond(200, "PetSyn Minimal WebSocket Server is running\n")


def create_minimal_config():
    """Create a minimal configuration for testing."""
    return {
        "server": {
            "ip": "0.0.0.0",
            "port": 8000
        },
        "selected_module": {},
        "LLM": {}
    }


async def main():
    """Main entry point for the minimal server."""
    try:
        # Try to load config from API, fall back to minimal config
        try:
            config = get_config_from_api({})
            if config is None:
                raise Exception("Failed to get config from API")
        except Exception as e:
            print(f"Warning: Could not load config from API ({e}), using minimal config")
            config = create_minimal_config()
        
        server = MinimalWebSocketServer(config)
        await server.start()
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
