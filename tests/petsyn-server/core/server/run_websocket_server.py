#!/usr/bin/env python3
"""
Entry point script for running the PetSyn WebSocket server.

This script provides multiple ways to run the server:
1. Full server with all features (if dependencies are available)
2. Minimal server for testing and development

Usage:
    python run_websocket_server.py [--minimal]
    
Options:
    --minimal    Run the minimal server for testing
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="PetSyn WebSocket Server")
    parser.add_argument(
        "--minimal", 
        action="store_true", 
        help="Run minimal server for testing"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="Host to bind to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind to (default: 8000)"
    )
    return parser.parse_args()


async def run_minimal_server(host="0.0.0.0", port=8000):
    """Run the minimal WebSocket server."""
    try:
        from petsyn_server.core.server.websocket_server_minimal import MinimalWebSocketServer, create_minimal_config
        
        config = create_minimal_config()
        config["server"]["ip"] = host
        config["server"]["port"] = port
        
        server = MinimalWebSocketServer(config)
        print(f"Starting minimal WebSocket server on {host}:{port}")
        await server.start()
        
    except ImportError as e:
        print(f"Error importing minimal server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error running minimal server: {e}")
        sys.exit(1)


async def run_full_server(host="0.0.0.0", port=8000):
    """Run the full WebSocket server."""
    try:
        from petsyn_server.config.config_loader import load_config
        from petsyn_server.core.server.websocket_server import WebSocketServer
        
        # Load configuration
        config = load_config()
        if config is None:
            print("Error: Could not load configuration")
            sys.exit(1)
        
        # Override host and port if specified
        if "server" not in config:
            config["server"] = {}
        config["server"]["ip"] = host
        config["server"]["port"] = port
        
        server = WebSocketServer(config)
        print(f"Starting full WebSocket server on {host}:{port}")
        await server.start()
        
    except ImportError as e:
        print(f"Error importing full server (some dependencies may be missing): {e}")
        print("Try running with --minimal flag for a basic server")
        sys.exit(1)
    except Exception as e:
        print(f"Error running full server: {e}")
        print("Try running with --minimal flag for a basic server")
        sys.exit(1)


async def main():
    """Main entry point."""
    args = parse_args()
    
    try:
        if args.minimal:
            await run_minimal_server(args.host, args.port)
        else:
            # Try full server first, fall back to minimal if it fails
            try:
                await run_full_server(args.host, args.port)
            except Exception as e:
                print(f"Full server failed: {e}")
                print("Falling back to minimal server...")
                await run_minimal_server(args.host, args.port)
                
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
