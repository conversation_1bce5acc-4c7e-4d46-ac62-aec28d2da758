"""
Endpoint Configuration

Defines the API endpoints, their schemas, and documentation for the PetSyn server.
This configuration drives the dynamic generation of the testing interface.
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional


@dataclass
class EndpointConfig:
    """Configuration for an API endpoint."""
    path: str
    methods: List[str]
    description: str
    requires_auth: bool = False
    request_schema: Optional[Dict[str, Any]] = None
    response_schema: Optional[Dict[str, Any]] = None
    examples: Optional[Dict[str, Any]] = None


# Define all available endpoints
ENDPOINTS = {
    "Vision API": EndpointConfig(
        path="/vision/explain",
        methods=["POST", "GET", "OPTIONS"],
        description="Vision analysis API for processing images with AI. Upload an image and ask questions about it. Requires authentication for POST requests.",
        requires_auth=True,
        request_schema={
            "POST": {
                "type": "multipart/form-data",
                "fields": {
                    "question": {
                        "type": "string",
                        "required": True,
                        "description": "Question to ask about the uploaded image"
                    },
                    "image": {
                        "type": "file",
                        "required": True,
                        "description": "Image file to analyze (JPEG, PNG, GIF, BMP, TIFF, WEBP)",
                        "max_size": "5MB"
                    }
                }
            }
        },
        response_schema={
            "GET": {
                "type": "text/plain",
                "description": "Status message about the vision service"
            },
            "POST": {
                "type": "application/json",
                "properties": {
                    "success": {
                        "type": "boolean",
                        "description": "Whether the request was successful"
                    },
                    "action": {
                        "type": "string",
                        "description": "Action type (usually 'RESPONSE')"
                    },
                    "response": {
                        "type": "string",
                        "description": "AI-generated response about the image"
                    },
                    "message": {
                        "type": "string",
                        "description": "Error message (only present if success is false)"
                    }
                }
            }
        },
        examples={
            "POST": {
                "request": {
                    "question": "What do you see in this image?",
                    "image": "example.jpg"
                },
                "response": {
                    "success": True,
                    "action": "RESPONSE",
                    "response": "I can see a beautiful landscape with mountains in the background and a lake in the foreground. The image appears to be taken during sunset with warm golden lighting."
                }
            },
            "GET": {
                "response": "MCP Vision interface is running normally, vision explanation interface address is: http://localhost:8003/vision/explain"
            }
        }
    ),
    
    # "OTA Update (Disabled)": EndpointConfig(
    #     path="/petsyn/ota/",
    #     methods=["GET", "POST", "OPTIONS"],
    #     description="Over-The-Air update endpoint for device firmware updates. Currently disabled in the server configuration.",
    #     requires_auth=False,
    #     request_schema={
    #         "POST": {
    #             "type": "application/json",
    #             "properties": {
    #                 "application": {
    #                     "type": "object",
    #                     "properties": {
    #                         "version": {
    #                             "type": "string",
    #                             "description": "Current application version"
    #                         }
    #                     }
    #                 }
    #             }
    #         }
    #     },
    #     response_schema={
    #         "GET": {
    #             "type": "text/plain",
    #             "description": "OTA service status message"
    #         },
    #         "POST": {
    #             "type": "application/json",
    #             "properties": {
    #                 "server_time": {
    #                     "type": "object",
    #                     "properties": {
    #                         "timestamp": {
    #                             "type": "integer",
    #                             "description": "Server timestamp in milliseconds"
    #                         },
    #                         "timezone_offset": {
    #                             "type": "integer",
    #                             "description": "Timezone offset in minutes"
    #                         }
    #                     }
    #                 },
    #                 "firmware": {
    #                     "type": "object",
    #                     "properties": {
    #                         "version": {
    #                             "type": "string",
    #                             "description": "Latest firmware version"
    #                         },
    #                         "url": {
    #                             "type": "string",
    #                             "description": "Download URL for firmware update"
    #                         }
    #                     }
    #                 },
    #                 "websocket": {
    #                     "type": "object",
    #                     "properties": {
    #                         "url": {
    #                             "type": "string",
    #                             "description": "WebSocket connection URL"
    #                         }
    #                     }
    #                 }
    #             }
    #         }
    #     },
    #     examples={
    #         "POST": {
    #             "request": {
    #                 "application": {
    #                     "version": "1.0.0"
    #                 }
    #             },
    #             "response": {
    #                 "server_time": {
    #                     "timestamp": 1640995200000,
    #                     "timezone_offset": 480
    #                 },
    #                 "firmware": {
    #                     "version": "1.0.0",
    #                     "url": ""
    #                 },
    #                 "websocket": {
    #                     "url": "ws://*************:8000/petsyn/v1/"
    #                 }
    #             }
    #         }
    #     }
    # )
}


def get_endpoint_documentation(endpoint_name: str) -> Dict[str, Any]:
    """
    Get comprehensive documentation for an endpoint.
    
    Args:
        endpoint_name: Name of the endpoint
        
    Returns:
        Dictionary containing endpoint documentation
    """
    if endpoint_name not in ENDPOINTS:
        return {"error": f"Endpoint '{endpoint_name}' not found"}
    
    config = ENDPOINTS[endpoint_name]
    
    return {
        "name": endpoint_name,
        "path": config.path,
        "methods": config.methods,
        "description": config.description,
        "requires_authentication": config.requires_auth,
        "request_schema": config.request_schema,
        "response_schema": config.response_schema,
        "examples": config.examples,
        "authentication_headers": [
            "Authorization: Bearer <token>",
            "Device-Id: <device_id>",
            "Client-Id: <client_id> (optional)"
        ] if config.requires_auth else None
    }


def get_all_endpoints_summary() -> Dict[str, Any]:
    """
    Get a summary of all available endpoints.
    
    Returns:
        Dictionary containing summary of all endpoints
    """
    summary = {
        "total_endpoints": len(ENDPOINTS),
        "endpoints": {}
    }
    
    for name, config in ENDPOINTS.items():
        summary["endpoints"][name] = {
            "path": config.path,
            "methods": config.methods,
            "description": config.description,
            "requires_auth": config.requires_auth
        }
    
    return summary


def validate_request_data(endpoint_name: str, method: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate request data against the endpoint schema.
    
    Args:
        endpoint_name: Name of the endpoint
        method: HTTP method
        data: Request data to validate
        
    Returns:
        Dictionary containing validation results
    """
    if endpoint_name not in ENDPOINTS:
        return {"valid": False, "errors": [f"Unknown endpoint: {endpoint_name}"]}
    
    config = ENDPOINTS[endpoint_name]
    
    if method not in config.methods:
        return {"valid": False, "errors": [f"Method {method} not supported for {endpoint_name}"]}
    
    if not config.request_schema or method not in config.request_schema:
        return {"valid": True, "errors": []}  # No schema to validate against
    
    schema = config.request_schema[method]
    errors = []
    
    # Basic validation for multipart form data
    if schema.get("type") == "multipart/form-data":
        fields = schema.get("fields", {})
        for field_name, field_config in fields.items():
            if field_config.get("required", False) and field_name not in data:
                errors.append(f"Required field '{field_name}' is missing")
            
            if field_name in data:
                if field_config.get("type") == "file" and not hasattr(data[field_name], 'read'):
                    errors.append(f"Field '{field_name}' must be a file")
    
    return {"valid": len(errors) == 0, "errors": errors}


# Authentication configuration
AUTH_CONFIG = {
    "token_header": "Authorization",
    "token_prefix": "Bearer ",
    "device_id_header": "Device-Id",
    "client_id_header": "Client-Id",
    "description": "Authentication is required for most endpoints. You need to provide a valid Bearer token and Device ID."
}


# Server configuration defaults
SERVER_CONFIG = {
    "default_host": "localhost",
    "default_port": 8003,
    "default_protocol": "http",
    "timeout": 30,
    "max_file_size": 5 * 1024 * 1024,  # 5MB
    "supported_image_formats": ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"]
}
