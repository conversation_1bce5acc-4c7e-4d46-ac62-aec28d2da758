"""
Authentication Utilities for PetSyn API Testing Interface

This module provides utilities for generating test authentication tokens
and understanding the PetSyn authentication system.
"""

import uuid
import time
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Tuple, Optional

# Import the actual AuthToken class from the server
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

try:
    from petsyn_server.core.utils.auth import AuthToken
    PETSYN_AUTH_AVAILABLE = True
except ImportError:
    PETSYN_AUTH_AVAILABLE = False
    print("Warning: PetSyn auth module not available. Using mock implementation.")


class AuthHelper:
    """Helper class for PetSyn authentication testing."""
    
    def __init__(self, auth_key: str = None):
        """
        Initialize the auth helper.
        
        Args:
            auth_key: The server's auth key (from config["server"]["auth_key"])
        """
        self.auth_key = auth_key or self._generate_default_auth_key()
        
        if PETSYN_AUTH_AVAILABLE:
            self.auth_token = AuthToken(self.auth_key)
        else:
            self.auth_token = None
    
    def _generate_default_auth_key(self) -> str:
        """Generate a default auth key similar to the server's logic."""
        return str(uuid.uuid4().hex)
    
    def generate_test_token(self, device_id: str) -> str:
        """
        Generate a test JWT token for the given device ID.
        
        Args:
            device_id: The device ID to generate a token for
            
        Returns:
            JWT token string
        """
        if not PETSYN_AUTH_AVAILABLE or not self.auth_token:
            # Return a mock token for testing
            return f"mock_token_for_{device_id}_{int(time.time())}"
        
        return self.auth_token.generate_token(device_id)
    
    def verify_test_token(self, token: str) -> Tuple[bool, Optional[str]]:
        """
        Verify a test token.
        
        Args:
            token: The token to verify
            
        Returns:
            Tuple of (is_valid, device_id)
        """
        if not PETSYN_AUTH_AVAILABLE or not self.auth_token:
            # Mock verification
            if token.startswith("mock_token_for_"):
                parts = token.split("_")
                if len(parts) >= 4:
                    device_id = "_".join(parts[3:-1])  # Extract device_id
                    return True, device_id
            return False, None
        
        return self.auth_token.verify_token(token)
    
    def get_test_credentials(self) -> Dict[str, Any]:
        """
        Get a set of test credentials for API testing.
        
        Returns:
            Dictionary containing test authentication credentials
        """
        # Generate test device IDs
        test_devices = [
            "test-device-001",
            "test-device-002", 
            "api-tester-001"
        ]
        
        credentials = {
            "auth_key": self.auth_key,
            "test_accounts": []
        }
        
        for device_id in test_devices:
            token = self.generate_test_token(device_id)
            credentials["test_accounts"].append({
                "device_id": device_id,
                "bearer_token": token,
                "client_id": f"client-{device_id}",
                "description": f"Test account for {device_id}"
            })
        
        return credentials
    
    def get_auth_instructions(self) -> Dict[str, Any]:
        """
        Get detailed instructions for authentication setup.
        
        Returns:
            Dictionary containing authentication setup instructions
        """
        return {
            "overview": {
                "title": "PetSyn Authentication System",
                "description": "PetSyn uses a dual-layer authentication system with JWT tokens and device verification."
            },
            "authentication_types": {
                "jwt_tokens": {
                    "description": "JWT tokens generated by the server's AuthToken class",
                    "format": "Bearer <jwt_token>",
                    "expiration": "1 hour from generation",
                    "encryption": "AES-GCM encrypted payload within JWT"
                },
                "static_tokens": {
                    "description": "Pre-configured static tokens in server configuration",
                    "location": "config.yaml -> server.auth.tokens",
                    "format": "Simple string tokens",
                    "default_enabled": False
                }
            },
            "required_headers": {
                "Authorization": {
                    "format": "Bearer <token>",
                    "required": True,
                    "description": "JWT token or static token"
                },
                "Device-Id": {
                    "format": "string",
                    "required": True,
                    "description": "Unique device identifier"
                },
                "Client-Id": {
                    "format": "string", 
                    "required": False,
                    "description": "Optional client identifier"
                }
            },
            "server_configuration": {
                "auth_enabled": {
                    "path": "config.yaml -> server.auth.enabled",
                    "default": False,
                    "description": "Enable/disable authentication"
                },
                "static_tokens": {
                    "path": "config.yaml -> server.auth.tokens",
                    "format": [
                        {"token": "petsyn-001", "name": "petsyn-litter-001"},
                        {"token": "petsyn-001", "name": "petsyn-feeder-001"},
                        {"token": "petsyn-001", "name": "petsyn-water-001"}
                    ]
                },
                "auth_key": {
                    "path": "Generated from manager-api.secret or UUID",
                    "description": "Used for JWT token signing"
                }
            },
            "setup_steps": [
                "1. Check if authentication is enabled in your server config",
                "2. If using static tokens, configure them in config.yaml",
                "3. If using JWT tokens, obtain the server's auth_key",
                "4. Generate JWT tokens using the AuthToken class",
                "5. Use the token in Authorization header as 'Bearer <token>'",
                "6. Include Device-Id header matching the token's device_id"
            ]
        }


def get_default_test_credentials() -> Dict[str, Any]:
    """
    Get default test credentials that work with the current server setup.
    
    Returns:
        Dictionary containing default test credentials
    """
    # Based on the server logs, we can see the auth_key being generated
    # For testing, we'll provide both static token examples and JWT examples
    
    return {
        "static_tokens": {
            "description": "These are the default static tokens from config.yaml",
            "note": "Authentication is disabled by default (server.auth.enabled: false)",
            "tokens": [
                {
                    "bearer_token": "petsyn-001",
                    "device_id": "petsyn-litter-001",
                    "client_id": "client-001",
                    "description": "Default static token 1 from config.yaml"
                },
                {
                    "bearer_token": "petsyn-001",
                    "device_id": "petsyn-feeder-001",
                    "client_id": "client-001",
                    "description": "Default static token 2 from config.yaml"
                },
                {
                    "bearer_token": "petsyn-001", 
                    "device_id": "petsyn-water-001",
                    "client_id": "client-001",
                    "description": "Default static token 3 from config.yaml"
                }
            ]
        },
        "jwt_tokens": {
            "description": "JWT tokens generated using the server's auth_key",
            "note": "These require the actual server auth_key to generate",
            "auth_key_source": "Generated from manager-api.secret or UUID in app.py",
            "example_device_ids": [
                "test-device-001",
                "test-device-002", 
                "api-tester-001"
            ]
        },
        "current_server_status": {
            "auth_enabled": False,
            "auth_key": "Generated at runtime (see server logs)",
            "static_tokens_configured": True,
            "recommendation": "Use static tokens for testing since auth is disabled"
        }
    }


def extract_auth_key_from_logs(log_text: str) -> Optional[str]:
    """
    Extract the auth_key from server log output.
    
    Args:
        log_text: Server log text containing the auth_key
        
    Returns:
        The extracted auth_key or None if not found
    """
    import re
    
    # Look for the auth_key in the server log format
    pattern = r"'auth_key':\s*'([a-f0-9]{32})'"
    match = re.search(pattern, log_text)
    
    if match:
        return match.group(1)
    
    return None


def create_test_auth_helper(auth_key: str = None) -> AuthHelper:
    """
    Create an AuthHelper instance for testing.
    
    Args:
        auth_key: Optional auth key from server logs
        
    Returns:
        AuthHelper instance
    """
    return AuthHelper(auth_key)


# Example usage and test data
EXAMPLE_CREDENTIALS = {
    "working_example": {
        "description": "Example that should work with default server setup",
        "bearer_token": "your-token1",
        "device_id": "your-device-name1", 
        "client_id": "test-client-001",
        "note": "Uses default static token from config.yaml"
    },
    "server_logs_example": {
        "description": "Extract auth_key from server logs like this:",
        "log_line": "Server started: {'auth_key': 'a5a89052fa4141a1b70a49e425cfb561'}",
        "extracted_auth_key": "a5a89052fa4141a1b70a49e425cfb561"
    }
}
