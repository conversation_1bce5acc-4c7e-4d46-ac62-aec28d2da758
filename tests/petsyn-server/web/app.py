"""
PetSyn Server API Testing Interface

A comprehensive Streamlit-based web interface for testing the PetSyn HTTP API endpoints.
This interface provides an interactive way to test API endpoints with custom inputs,
view responses, and maintain request history.
"""

import streamlit as st
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import base64
from io import BytesIO
from PIL import Image

from api_client import PetSynAPIClient
from endpoint_config import ENDPOINTS, EndpointConfig
from auth_utils import get_default_test_credentials, create_test_auth_helper, extract_auth_key_from_logs

# Page configuration
st.set_page_config(
    page_title="PetSyn API Doc",
    page_icon="🐾",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .endpoint-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .method-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }
    
    .method-get { background-color: #28a745; color: white; }
    .method-post { background-color: #007bff; color: white; }
    .method-put { background-color: #ffc107; color: black; }
    .method-delete { background-color: #dc3545; color: white; }
    .method-options { background-color: #6c757d; color: white; }
    
    .response-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 4px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .response-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .sidebar .sidebar-content {
        background-color: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'api_client' not in st.session_state:
        st.session_state.api_client = None
    if 'request_history' not in st.session_state:
        st.session_state.request_history = []
    if 'server_url' not in st.session_state:
        st.session_state.server_url = "http://localhost:8003"

    # Authentication settings
    if 'auth_bearer_token' not in st.session_state:
        st.session_state.auth_bearer_token = ""
    if 'auth_device_id' not in st.session_state:
        st.session_state.auth_device_id = ""
    if 'auth_client_id' not in st.session_state:
        st.session_state.auth_client_id = ""
    if 'auth_preset_selected' not in st.session_state:
        st.session_state.auth_preset_selected = "Custom"
    if 'auth_server_key' not in st.session_state:
        st.session_state.auth_server_key = ""

def render_header():
    """Render the main header."""
    st.markdown("""
    <div class="main-header">
        <h1>🐾 PetSyn Server API Doc</h1>
        <p>Interactive testing tool for PetSyn API endpoints</p>
    </div>
    """, unsafe_allow_html=True)

def render_authentication_settings():
    """Render the authentication settings section in the sidebar."""
    st.sidebar.title("🔐 Authentication Settings")

    # Get default credentials for reference
    default_creds = get_default_test_credentials()

    # Authentication preset selection
    preset_options = ["Custom", "Static Token of Litter", "Static Token of Feeder", "Static Token of Water"]
    auth_preset = st.sidebar.selectbox(
        "Authentication Preset",
        preset_options,
        index=preset_options.index(st.session_state.auth_preset_selected),
        help="Choose a preset or configure custom authentication"
    )

    if auth_preset != st.session_state.auth_preset_selected:
        st.session_state.auth_preset_selected = auth_preset

        # Apply preset values
        if auth_preset == "Static Token of Litter":
            token_data = default_creds["static_tokens"]["tokens"][0]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Static Token of Feeder":
            token_data = default_creds["static_tokens"]["tokens"][1]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Static Token of Water":
            token_data = default_creds["static_tokens"]["tokens"][2]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Generate JWT":
            # Show JWT generation section
            pass

    # Authentication fields
    st.session_state.auth_bearer_token = st.sidebar.text_input(
        "Bearer Token",
        value=st.session_state.auth_bearer_token,
        type="password",
        help="Enter your authentication token (without 'Bearer ' prefix)"
    )

    st.session_state.auth_device_id = st.sidebar.text_input(
        "Device ID",
        value=st.session_state.auth_device_id,
        help="Enter your device identifier"
    )

    st.session_state.auth_client_id = st.sidebar.text_input(
        "Client ID (Optional)",
        value=st.session_state.auth_client_id,
        help="Enter your client identifier (optional)"
    )

    # Authentication status
    if st.session_state.auth_bearer_token and st.session_state.auth_device_id:
        st.sidebar.success("✅ Authentication configured")
    else:
        st.sidebar.warning("⚠️ Authentication not configured")


def render_sidebar():
    """Render the sidebar with server configuration and history."""
    st.sidebar.title("⚙️ Server Configuration")

    # Server URL configuration
    server_url = st.sidebar.text_input(
        "Server URL",
        value=st.session_state.server_url,
        help="Enter the base URL of your PetSyn server"
    )

    if server_url != st.session_state.server_url:
        st.session_state.server_url = server_url
        st.session_state.api_client = PetSynAPIClient(server_url) # type: ignore

    if st.session_state.api_client is None:
        st.session_state.api_client = PetSynAPIClient(server_url) # type: ignore

    # Test connection
    if st.sidebar.button("🔍 Test Connection"):
        with st.sidebar:
            with st.spinner("Testing connection..."):
                try:
                    response = st.session_state.api_client.test_connection()
                    if response.get('success', False):
                        st.success("✅ Connection successful!")
                    else:
                        st.warning("⚠️ Server responded but may have issues")
                except Exception as e:
                    st.error(f"❌ Connection failed: {str(e)}")

    st.sidebar.markdown("---")

    # Authentication settings
    render_authentication_settings()

    st.sidebar.markdown("---")

    # Request history
    st.sidebar.title("📋 Request History")
    if st.session_state.request_history:
        for i, request in enumerate(reversed(st.session_state.request_history[-10:])):
            with st.sidebar.expander(f"{request['method']} {request['endpoint']} - {request['timestamp'][:19]}"):
                st.write(f"**Status:** {request['status_code']}")
                st.write(f"**Duration:** {request['duration']:.2f}s")
                if request.get('error'):
                    st.error(f"Error: {request['error']}")
    else:
        st.sidebar.info("No requests made yet")

    if st.sidebar.button("🗑️ Clear History"):
        st.session_state.request_history = []
        st.rerun()

def render_endpoint_card(endpoint_name: str, config: EndpointConfig):
    """Render an endpoint testing card."""
    st.markdown(f"""
    <div class="endpoint-card">
        <h3>{endpoint_name}</h3>
        <p>{config.description}</p>
    </div>
    """, unsafe_allow_html=True)

    # Method selection
    col1, col2 = st.columns([1, 3])
    with col1:
        method = st.selectbox(
            "Method",
            config.methods,
            key=f"{endpoint_name}_method"
        )

    with col2:
        st.markdown(f"""
        <span class="method-badge method-{method.lower()}">{method}</span>
        <code>{config.path}</code>
        """, unsafe_allow_html=True)

    # Authentication status display
    if config.requires_auth:
        auth_configured = (st.session_state.auth_bearer_token and
                          st.session_state.auth_device_id)

        if auth_configured:
            st.success("🔐 Authentication: Configured in sidebar")
        else:
            st.warning("🔐 Authentication: Please configure in sidebar")
            st.info("💡 This endpoint requires authentication. Configure your credentials in the sidebar.")

    # Request parameters
    if method in ['POST', 'PUT'] and config.request_schema:
        st.subheader("📝 Request Parameters")
        request_data = render_request_form(endpoint_name, config, method)
    else:
        request_data = {}

    # Send request button
    if st.button(f"🚀 Send {method} Request", key=f"{endpoint_name}_send"):
        headers = {}

        # Use centralized authentication settings
        if config.requires_auth:
            if st.session_state.auth_bearer_token:
                headers['Authorization'] = f"Bearer {st.session_state.auth_bearer_token}"
            if st.session_state.auth_device_id:
                headers['Device-Id'] = st.session_state.auth_device_id
            if st.session_state.auth_client_id:
                headers['Client-Id'] = st.session_state.auth_client_id

        with st.spinner("Sending request..."):
            try:
                start_time = time.time()
                response = st.session_state.api_client.make_request(
                    method=method,
                    endpoint=config.path,
                    data=request_data,
                    headers=headers
                )
                duration = time.time() - start_time

                # Add to history
                st.session_state.request_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'method': method,
                    'endpoint': config.path,
                    'status_code': response.get('status_code', 'Unknown'),
                    'duration': duration,
                    'response': response,
                    'error': response.get('error')
                })

                # Display response
                render_response(response, duration)

            except Exception as e:
                st.error(f"❌ Request failed: {str(e)}")
                st.session_state.request_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'method': method,
                    'endpoint': config.path,
                    'status_code': 'Error',
                    'duration': 0,
                    'error': str(e)
                })

def render_request_form(endpoint_name: str, config: EndpointConfig, method: str) -> Dict[str, Any]:
    """Render the request form based on endpoint configuration."""
    request_data = {}
    
    if endpoint_name == "Vision API" and method == "POST":
        # Special handling for Vision API multipart form
        question = st.text_area(
            "Question",
            placeholder="Enter your question about the image...",
            key=f"{endpoint_name}_question"
        )
        
        uploaded_file = st.file_uploader(
            "Upload Image",
            type=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'],
            key=f"{endpoint_name}_image"
        )
        
        if uploaded_file is not None:
            # Display image preview
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Image", use_column_width=True)
            
            # Prepare multipart data
            request_data = {
                'question': question,
                'image': uploaded_file
            }
    
    return request_data

def render_response(response: Dict[str, Any], duration: float):
    """Render the API response."""
    st.subheader("📤 Response")
    
    # Response metadata
    col1, col2, col3 = st.columns(3)
    with col1:
        status_code = response.get('status_code', 'Unknown')
        if isinstance(status_code, int) and 200 <= status_code < 300:
            st.success(f"Status: {status_code}")
        else:
            st.error(f"Status: {status_code}")
    
    with col2:
        st.info(f"Duration: {duration:.2f}s")
    
    with col3:
        response_size = len(str(response.get('data', '')))
        st.info(f"Size: {response_size} bytes")
    
    # Response body
    if response.get('error'):
        st.markdown(f"""
        <div class="response-error">
            <strong>Error:</strong> {response['error']}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown('<div class="response-success">', unsafe_allow_html=True)
        
        # Format JSON response
        if response.get('data'):
            try:
                if isinstance(response['data'], str):
                    json_data = json.loads(response['data'])
                else:
                    json_data = response['data']
                st.json(json_data)
            except json.JSONDecodeError:
                st.text(response['data'])
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Response headers
    if response.get('headers'):
        with st.expander("📋 Response Headers"):
            st.json(dict(response['headers']))

def render_authentication_status():
    """Render authentication status and instructions in the main area."""
    st.subheader("🔐 Authentication Status")

    # Current authentication status
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.session_state.auth_bearer_token:
            st.success("✅ Bearer Token Set")
        else:
            st.error("❌ Bearer Token Missing")

    with col2:
        if st.session_state.auth_device_id:
            st.success("✅ Device ID Set")
        else:
            st.error("❌ Device ID Missing")

    with col3:
        if st.session_state.auth_client_id:
            st.success("✅ Client ID Set")
        else:
            st.info("ℹ️ Client ID Optional")


def main():
    """Main application function."""
    initialize_session_state()
    render_header()
    render_sidebar()

    # Authentication status section
    render_authentication_status()
    st.markdown("---")

    # Main content area
    st.title("🔧 API Endpoints")

    # Render endpoint testing interfaces
    for endpoint_name, config in ENDPOINTS.items():
        with st.expander(f"🔗 {endpoint_name}", expanded=True):
            render_endpoint_card(endpoint_name, config)
        st.markdown("---")

    # Footer
    st.markdown("""
    ---
    <div style="text-align: center; color: #666; padding: 2rem;">
        <p>🐾 PetSyn Server API Testing Interface | Built with Streamlit</p>
        <p>For more information, visit the <a href="https://github.com/your-repo/petsyn-server" target="_blank">PetSyn Server Repository</a></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
