"""
PetSyn Server API Testing Interface

A comprehensive Streamlit-based web interface for testing the PetSyn HTTP API endpoints.
This interface provides an interactive way to test API endpoints with custom inputs,
view responses, and maintain request history.
"""

import streamlit as st
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import base64
from io import BytesIO
from PIL import Image

from api_client import PetSynAPIClient
from endpoint_config import ENDPOINTS, EndpointConfig
from auth_utils import get_default_test_credentials, create_test_auth_helper, extract_auth_key_from_logs

# Page configuration
st.set_page_config(
    page_title="PetSynanpse API Doc",
    page_icon="🐾",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for modern, clean styling inspired by Perplexity AI
st.markdown("""
<style>
    /* Global Styles */
    .stApp {
        background-color: #fafafa;
    }

    /* Main Header */
    .main-header {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        padding: 2.5rem 2rem;
        border-radius: 16px;
        color: #1e293b;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .main-header h1 {
        font-weight: 700;
        font-size: 2.25rem;
        margin-bottom: 0.5rem;
        color: #0f172a;
    }

    .main-header p {
        font-size: 1.125rem;
        color: #64748b;
        margin: 0;
    }

    /* Endpoint Cards */
    .endpoint-card {
        background: #ffffff;
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.2s ease-in-out;
    }

    .endpoint-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: #cbd5e1;
    }

    .endpoint-card h3 {
        color: #0f172a;
        font-weight: 600;
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .endpoint-card p {
        color: #64748b;
        font-size: 0.95rem;
        line-height: 1.6;
        margin: 0;
    }

    /* Method Badges */
    .method-badge {
        display: inline-block;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        font-weight: 600;
        font-size: 0.75rem;
        margin-right: 0.75rem;
        letter-spacing: 0.025em;
        text-transform: uppercase;
    }

    .method-get {
        background-color: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }
    .method-post {
        background-color: #dbeafe;
        color: #1e40af;
        border: 1px solid #bfdbfe;
    }
    .method-put {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fde68a;
    }
    .method-delete {
        background-color: #fee2e2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }
    .method-options {
        background-color: #f1f5f9;
        color: #475569;
        border: 1px solid #e2e8f0;
    }

    /* Response Containers */
    .response-success {
        background-color: #f0fdf4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 1.25rem;
        margin: 1.5rem 0;
    }

    .response-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 8px;
        padding: 1.25rem;
        margin: 1.5rem 0;
    }

    /* Sidebar Styling */
    .css-1d391kg {
        background-color: #ffffff;
        border-right: 1px solid #e2e8f0;
    }

    /* Status Indicators */
    .status-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin-bottom: 0.75rem;
        text-align: center;
        font-size: 0.875rem;
    }

    .status-success {
        border-color: #bbf7d0;
        background-color: #f0fdf4;
    }

    .status-warning {
        border-color: #fde68a;
        background-color: #fffbeb;
    }

    .status-error {
        border-color: #fecaca;
        background-color: #fef2f2;
    }

    /* Compact authentication section styling */
    .auth-section .stColumns {
        gap: 0.75rem;
    }

    .auth-section .stColumn {
        padding: 0;
    }

    /* Reduce default Streamlit spacing in auth section */
    .auth-section .element-container {
        margin-bottom: 0;
    }

    /* Authentication Section */
    .auth-section {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem 1.5rem 0.5rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .auth-section h3 {
        color: #0f172a;
        font-weight: 600;
        margin-bottom: 0.75rem;
        font-size: 1.125rem;
    }

    /* Clean dividers */
    .section-divider {
        border: none;
        height: 1px;
        background: linear-gradient(to right, transparent, #e2e8f0, transparent);
        margin: 1.5rem 0;
    }

    /* Button styling improvements */
    .stButton > button {
        background-color: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
    }

    .stButton > button:hover {
        background-color: #2563eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* Input field improvements */
    .stTextInput > div > div > input {
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 0.75rem;
    }

    .stTextInput > div > div > input:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Selectbox improvements */
    .stSelectbox > div > div > select {
        border: 1px solid #d1d5db;
        border-radius: 6px;
    }

    /* File uploader improvements */
    .stFileUploader > div {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        background-color: #fafafa;
    }

    /* Expander improvements */
    .streamlit-expanderHeader {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1rem;
    }

    /* Code block improvements */
    .stCode {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
    }

    /* JSON display improvements */
    .stJson {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
    }

    /* Sidebar improvements */
    .css-1d391kg .css-1v0mbdj {
        padding-top: 2rem;
    }

    /* Expander header styling */
    .css-1kyxreq {
        background-color: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    /* Success/Error message improvements */
    .stSuccess {
        background-color: #f0fdf4;
        border: 1px solid #bbf7d0;
        border-radius: 6px;
        color: #166534;
    }

    .stError {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 6px;
        color: #dc2626;
    }

    .stWarning {
        background-color: #fffbeb;
        border: 1px solid #fde68a;
        border-radius: 6px;
        color: #92400e;
    }

    .stInfo {
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 6px;
        color: #0369a1;
    }

    /* Metric improvements */
    .css-1xarl3l {
        background-color: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1rem;
    }

    /* Spinner improvements */
    .stSpinner {
        color: #3b82f6;
    }

    /* Tab improvements */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }

    .stTabs [data-baseweb="tab"] {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        color: #64748b;
    }

    .stTabs [aria-selected="true"] {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }

    /* Progress bar improvements */
    .stProgress .css-1cpxqw2 {
        background-color: #e2e8f0;
        border-radius: 4px;
    }

    .stProgress .css-1cpxqw2 .css-1ht1j8u {
        background-color: #3b82f6;
        border-radius: 4px;
    }

    /* Dataframe improvements */
    .stDataFrame {
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Chart improvements */
    .stPlotlyChart {
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        background-color: #ffffff;
    }

    /* Image improvements */
    .stImage {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
    }

    /* Container spacing */
    .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Typography improvements */
    h1, h2, h3, h4, h5, h6 {
        color: #0f172a;
        font-weight: 600;
    }

    p {
        color: #374151;
        line-height: 1.6;
    }

    /* Link improvements */
    a {
        color: #3b82f6;
        text-decoration: none;
    }

    a:hover {
        color: #2563eb;
        text-decoration: underline;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'api_client' not in st.session_state:
        st.session_state.api_client = None
    if 'request_history' not in st.session_state:
        st.session_state.request_history = []
    if 'server_url' not in st.session_state:
        st.session_state.server_url = "http://localhost:8003"

    # Authentication settings
    if 'auth_bearer_token' not in st.session_state:
        st.session_state.auth_bearer_token = ""
    if 'auth_device_id' not in st.session_state:
        st.session_state.auth_device_id = ""
    if 'auth_client_id' not in st.session_state:
        st.session_state.auth_client_id = ""
    if 'auth_preset_selected' not in st.session_state:
        st.session_state.auth_preset_selected = "Custom"
    if 'auth_server_key' not in st.session_state:
        st.session_state.auth_server_key = ""

def render_header():
    """Render the main header with modern styling."""
    st.markdown("""
    <div class="main-header">
        <h1>🐾 PetSyn Server API Doc</h1>
        <p>Interactive testing tool for PetSyn API endpoints</p>
    </div>
    """, unsafe_allow_html=True)

def render_authentication_settings():
    """Render the authentication settings section in the sidebar."""
    st.sidebar.title("🔐 Authentication Settings")

    # Get default credentials for reference
    default_creds = get_default_test_credentials()

    # Authentication preset selection
    preset_options = ["Custom", "Static Token of Litter", "Static Token of Feeder", "Static Token of Water"]
    auth_preset = st.sidebar.selectbox(
        "Authentication Preset",
        preset_options,
        index=preset_options.index(st.session_state.auth_preset_selected),
        help="Choose a preset or configure custom authentication"
    )

    if auth_preset != st.session_state.auth_preset_selected:
        st.session_state.auth_preset_selected = auth_preset

        # Apply preset values
        if auth_preset == "Static Token of Litter":
            token_data = default_creds["static_tokens"]["tokens"][0]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Static Token of Feeder":
            token_data = default_creds["static_tokens"]["tokens"][1]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Static Token of Water":
            token_data = default_creds["static_tokens"]["tokens"][2]
            st.session_state.auth_bearer_token = token_data["bearer_token"]
            st.session_state.auth_device_id = token_data["device_id"]
            st.session_state.auth_client_id = token_data["client_id"]
        elif auth_preset == "Generate JWT":
            # Show JWT generation section
            pass

    # Authentication fields
    st.session_state.auth_bearer_token = st.sidebar.text_input(
        "Bearer Token",
        value=st.session_state.auth_bearer_token,
        type="password",
        help="Enter your authentication token (without 'Bearer ' prefix)"
    )

    st.session_state.auth_device_id = st.sidebar.text_input(
        "Device ID",
        value=st.session_state.auth_device_id,
        help="Enter your device identifier"
    )

    st.session_state.auth_client_id = st.sidebar.text_input(
        "Client ID (Optional)",
        value=st.session_state.auth_client_id,
        help="Enter your client identifier (optional)"
    )

    # Authentication status
    if st.session_state.auth_bearer_token and st.session_state.auth_device_id:
        st.sidebar.success("✅ Authentication configured")
    else:
        st.sidebar.warning("⚠️ Authentication not configured")


def render_sidebar():
    """Render the sidebar with server configuration and history."""
    st.sidebar.markdown("""
    <div style="padding: 1rem 0; border-bottom: 1px solid #e2e8f0; margin-bottom: 1.5rem;">
        <h3 style="color: #0f172a; font-weight: 600; margin: 0;">⚙️ Server Configuration</h3>
    </div>
    """, unsafe_allow_html=True)

    # Server URL configuration
    server_url = st.sidebar.text_input(
        "Server URL",
        value=st.session_state.server_url,
        help="Enter the base URL of your PetSyn server"
    )

    if server_url != st.session_state.server_url:
        st.session_state.server_url = server_url
        st.session_state.api_client = PetSynAPIClient(server_url) # type: ignore

    if st.session_state.api_client is None:
        st.session_state.api_client = PetSynAPIClient(server_url) # type: ignore

    # Test connection
    if st.sidebar.button("🔍 Test Connection"):
        with st.sidebar:
            with st.spinner("Testing connection..."):
                try:
                    response = st.session_state.api_client.test_connection()
                    if response.get('success', False):
                        st.success("✅ Connection successful!")
                    else:
                        st.warning("⚠️ Server responded but may have issues")
                except Exception as e:
                    st.error(f"❌ Connection failed: {str(e)}")

    st.sidebar.markdown("---")

    # Authentication settings
    render_authentication_settings()

    st.sidebar.markdown("---")

    # Request history
    st.sidebar.markdown("""
    <div style="padding: 1rem 0; border-bottom: 1px solid #e2e8f0; margin-bottom: 1.5rem;">
        <h3 style="color: #0f172a; font-weight: 600; margin: 0;">📋 Request History</h3>
    </div>
    """, unsafe_allow_html=True)
    if st.session_state.request_history:
        for i, request in enumerate(reversed(st.session_state.request_history[-10:])):
            with st.sidebar.expander(f"{request['method']} {request['endpoint']} - {request['timestamp'][:19]}"):
                st.write(f"**Status:** {request['status_code']}")
                st.write(f"**Duration:** {request['duration']:.2f}s")
                if request.get('error'):
                    st.error(f"Error: {request['error']}")
    else:
        st.sidebar.info("No requests made yet")

    if st.sidebar.button("🗑️ Clear History"):
        st.session_state.request_history = []
        st.rerun()

def render_endpoint_card(endpoint_name: str, config: EndpointConfig):
    """Render an endpoint testing card."""
    st.markdown(f"""
    <div class="endpoint-card">
        <h3>{endpoint_name}</h3>
        <p>{config.description}</p>
    </div>
    """, unsafe_allow_html=True)

    # Method selection
    col1, col2 = st.columns([1, 3])
    with col1:
        method = st.selectbox(
            "Method",
            config.methods,
            key=f"{endpoint_name}_method"
        )

    with col2:
        st.markdown(f"""
        <span class="method-badge method-{method.lower()}">{method}</span>
        <code>{config.path}</code>
        """, unsafe_allow_html=True)

    # Authentication status display
    if config.requires_auth:
        auth_configured = (st.session_state.auth_bearer_token and
                          st.session_state.auth_device_id)

        if auth_configured:
            st.success("🔐 Authentication: Configured in sidebar")
        else:
            st.warning("🔐 Authentication: Please configure in sidebar")
            st.info("💡 This endpoint requires authentication. Configure your credentials in the sidebar.")

    # Request parameters
    if method in ['POST', 'PUT'] and config.request_schema:
        st.subheader("📝 Request Parameters")
        request_data = render_request_form(endpoint_name, config, method)
    else:
        request_data = {}

    # Send request button
    if st.button(f"🚀 Send {method} Request", key=f"{endpoint_name}_send"):
        headers = {}

        # Use centralized authentication settings
        if config.requires_auth:
            if st.session_state.auth_bearer_token:
                headers['Authorization'] = f"Bearer {st.session_state.auth_bearer_token}"
            if st.session_state.auth_device_id:
                headers['Device-Id'] = st.session_state.auth_device_id
            if st.session_state.auth_client_id:
                headers['Client-Id'] = st.session_state.auth_client_id

        with st.spinner("Sending request..."):
            try:
                start_time = time.time()
                response = st.session_state.api_client.make_request(
                    method=method,
                    endpoint=config.path,
                    data=request_data,
                    headers=headers
                )
                duration = time.time() - start_time

                # Add to history
                st.session_state.request_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'method': method,
                    'endpoint': config.path,
                    'status_code': response.get('status_code', 'Unknown'),
                    'duration': duration,
                    'response': response,
                    'error': response.get('error')
                })

                # Display response
                render_response(response, duration)

            except Exception as e:
                st.error(f"❌ Request failed: {str(e)}")
                st.session_state.request_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'method': method,
                    'endpoint': config.path,
                    'status_code': 'Error',
                    'duration': 0,
                    'error': str(e)
                })

def render_request_form(endpoint_name: str, config: EndpointConfig, method: str) -> Dict[str, Any]:
    """Render the request form based on endpoint configuration."""
    request_data = {}
    
    if endpoint_name == "Vision API" and method == "POST":
        # Special handling for Vision API multipart form
        question = st.text_area(
            "Question",
            placeholder="Enter your question about the image...",
            key=f"{endpoint_name}_question"
        )
        
        uploaded_file = st.file_uploader(
            "Upload Image",
            type=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'],
            key=f"{endpoint_name}_image"
        )
        
        if uploaded_file is not None:
            # Display image preview
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Image", use_container_width=True)
            
            # Prepare multipart data
            request_data = {
                'question': question,
                'image': uploaded_file
            }
    
    return request_data

def render_response(response: Dict[str, Any], duration: float):
    """Render the API response."""
    st.subheader("📤 Response")
    
    # Response metadata
    col1, col2, col3 = st.columns(3)
    with col1:
        status_code = response.get('status_code', 'Unknown')
        if isinstance(status_code, int) and 200 <= status_code < 300:
            st.success(f"Status: {status_code}")
        else:
            st.error(f"Status: {status_code}")
    
    with col2:
        st.info(f"Duration: {duration:.2f}s")
    
    with col3:
        response_size = len(str(response.get('data', '')))
        st.info(f"Size: {response_size} bytes")
    
    # Response body
    if response.get('error'):
        st.markdown(f"""
        <div class="response-error">
            <strong>Error:</strong> {response['error']}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown('<div class="response-success">', unsafe_allow_html=True)
        
        # Format JSON response
        if response.get('data'):
            try:
                if isinstance(response['data'], str):
                    json_data = json.loads(response['data'])
                else:
                    json_data = response['data']
                st.json(json_data)
            except json.JSONDecodeError:
                st.text(response['data'])
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Response headers
    if response.get('headers'):
        with st.expander("📋 Response Headers"):
            st.json(dict(response['headers']))

def render_authentication_status():
    """Render authentication status and instructions in the main area."""
    st.markdown("""
    <div class="auth-section">
        <h3>🔐 Authentication Status</h3>
        <div style="margin-bottom: 0.5rem;">
    """, unsafe_allow_html=True)

    # Current authentication status with compact cards
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.session_state.auth_bearer_token:
            st.markdown("""
            <div class="status-card status-success">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">✅ Bearer Token</div>
                <div style="font-size: 0.75rem; color: #166534;">Configured</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="status-card status-error">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">❌ Bearer Token</div>
                <div style="font-size: 0.75rem; color: #dc2626;">Missing</div>
            </div>
            """, unsafe_allow_html=True)

    with col2:
        if st.session_state.auth_device_id:
            st.markdown("""
            <div class="status-card status-success">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">✅ Device ID</div>
                <div style="font-size: 0.75rem; color: #166534;">Configured</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="status-card status-error">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">❌ Device ID</div>
                <div style="font-size: 0.75rem; color: #dc2626;">Missing</div>
            </div>
            """, unsafe_allow_html=True)

    with col3:
        if st.session_state.auth_client_id:
            st.markdown("""
            <div class="status-card status-success">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">✅ Client ID</div>
                <div style="font-size: 0.75rem; color: #166534;">Configured</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="status-card status-warning">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">ℹ️ Client ID</div>
                <div style="font-size: 0.75rem; color: #92400e;">Optional</div>
            </div>
            """, unsafe_allow_html=True)

    st.markdown("</div></div>", unsafe_allow_html=True)


def main():
    """Main application function."""
    initialize_session_state()
    render_header()
    render_sidebar()

    # Authentication status section
    render_authentication_status()

    # Compact section divider
    st.markdown('<hr class="section-divider" style="margin: 1.5rem 0;">', unsafe_allow_html=True)

    # Main content area with modern styling
    st.markdown("""
    <div style="margin-bottom: 2rem;">
        <h2 style="color: #0f172a; font-weight: 600; font-size: 1.875rem; margin-bottom: 0.5rem;">🔧 API Endpoints</h2>
        <p style="color: #64748b; font-size: 1rem; margin: 0;">Test and interact with PetSyn server endpoints</p>
    </div>
    """, unsafe_allow_html=True)

    # Render endpoint testing interfaces
    for endpoint_name, config in ENDPOINTS.items():
        with st.expander(f"🔗 {endpoint_name}", expanded=True):
            render_endpoint_card(endpoint_name, config)
        st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # Modern footer
    st.markdown("""
    <div style="text-align: center; color: #64748b; padding: 3rem 2rem; margin-top: 2rem; border-top: 1px solid #e2e8f0;">
        <p style="font-size: 0.95rem; margin-bottom: 0.5rem; font-weight: 500;">🐾 PetSyn Server API Doc</p>
        <p style="font-size: 0.875rem; margin: 0; color: #94a3b8;">For more information, visit the <a href="https://petsynapse.com" target="_blank">PetSynapse Website</a></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
